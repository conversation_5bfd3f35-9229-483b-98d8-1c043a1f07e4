{"version": 3, "sources": ["../../echarts/lib/label/labelGuideHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { Point, Path, Polyline } from '../util/graphic.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nimport { cubicProjectPoint, quadraticProjectPoint } from 'zrender/lib/core/curve.js';\nimport { defaults, retrieve2 } from 'zrender/lib/core/util.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport { DISPLAY_STATES, SPECIAL_STATES } from '../util/states.js';\nvar PI2 = Math.PI * 2;\nvar CMD = PathProxy.CMD;\nvar DEFAULT_SEARCH_SPACE = ['top', 'right', 'bottom', 'left'];\nfunction getCandidateAnchor(pos, distance, rect, outPt, outDir) {\n  var width = rect.width;\n  var height = rect.height;\n  switch (pos) {\n    case 'top':\n      outPt.set(rect.x + width / 2, rect.y - distance);\n      outDir.set(0, -1);\n      break;\n    case 'bottom':\n      outPt.set(rect.x + width / 2, rect.y + height + distance);\n      outDir.set(0, 1);\n      break;\n    case 'left':\n      outPt.set(rect.x - distance, rect.y + height / 2);\n      outDir.set(-1, 0);\n      break;\n    case 'right':\n      outPt.set(rect.x + width + distance, rect.y + height / 2);\n      outDir.set(1, 0);\n      break;\n  }\n}\nfunction projectPointToArc(cx, cy, r, startAngle, endAngle, anticlockwise, x, y, out) {\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  x /= d;\n  y /= d;\n  // Intersect point.\n  var ox = x * r + cx;\n  var oy = y * r + cy;\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    // Is a circle\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n  var angle = Math.atan2(y, x);\n  if (angle < 0) {\n    angle += PI2;\n  }\n  if (angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle) {\n    // Project point is on the arc.\n    out[0] = ox;\n    out[1] = oy;\n    return d - r;\n  }\n  var x1 = r * Math.cos(startAngle) + cx;\n  var y1 = r * Math.sin(startAngle) + cy;\n  var x2 = r * Math.cos(endAngle) + cx;\n  var y2 = r * Math.sin(endAngle) + cy;\n  var d1 = (x1 - x) * (x1 - x) + (y1 - y) * (y1 - y);\n  var d2 = (x2 - x) * (x2 - x) + (y2 - y) * (y2 - y);\n  if (d1 < d2) {\n    out[0] = x1;\n    out[1] = y1;\n    return Math.sqrt(d1);\n  } else {\n    out[0] = x2;\n    out[1] = y2;\n    return Math.sqrt(d2);\n  }\n}\nfunction projectPointToLine(x1, y1, x2, y2, x, y, out, limitToEnds) {\n  var dx = x - x1;\n  var dy = y - y1;\n  var dx1 = x2 - x1;\n  var dy1 = y2 - y1;\n  var lineLen = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n  dx1 /= lineLen;\n  dy1 /= lineLen;\n  // dot product\n  var projectedLen = dx * dx1 + dy * dy1;\n  var t = projectedLen / lineLen;\n  if (limitToEnds) {\n    t = Math.min(Math.max(t, 0), 1);\n  }\n  t *= lineLen;\n  var ox = out[0] = x1 + t * dx1;\n  var oy = out[1] = y1 + t * dy1;\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nfunction projectPointToRect(x1, y1, width, height, x, y, out) {\n  if (width < 0) {\n    x1 = x1 + width;\n    width = -width;\n  }\n  if (height < 0) {\n    y1 = y1 + height;\n    height = -height;\n  }\n  var x2 = x1 + width;\n  var y2 = y1 + height;\n  var ox = out[0] = Math.min(Math.max(x, x1), x2);\n  var oy = out[1] = Math.min(Math.max(y, y1), y2);\n  return Math.sqrt((ox - x) * (ox - x) + (oy - y) * (oy - y));\n}\nvar tmpPt = [];\nfunction nearestPointOnRect(pt, rect, out) {\n  var dist = projectPointToRect(rect.x, rect.y, rect.width, rect.height, pt.x, pt.y, tmpPt);\n  out.set(tmpPt[0], tmpPt[1]);\n  return dist;\n}\n/**\r\n * Calculate min distance corresponding point.\r\n * This method won't evaluate if point is in the path.\r\n */\nfunction nearestPointOnPath(pt, path, out) {\n  var xi = 0;\n  var yi = 0;\n  var x0 = 0;\n  var y0 = 0;\n  var x1;\n  var y1;\n  var minDist = Infinity;\n  var data = path.data;\n  var x = pt.x;\n  var y = pt.y;\n  for (var i = 0; i < data.length;) {\n    var cmd = data[i++];\n    if (i === 1) {\n      xi = data[i];\n      yi = data[i + 1];\n      x0 = xi;\n      y0 = yi;\n    }\n    var d = minDist;\n    switch (cmd) {\n      case CMD.M:\n        // moveTo 命令重新创建一个新的 subpath, 并且更新新的起点\n        // 在 closePath 的时候使用\n        x0 = data[i++];\n        y0 = data[i++];\n        xi = x0;\n        yi = y0;\n        break;\n      case CMD.L:\n        d = projectPointToLine(xi, yi, data[i], data[i + 1], x, y, tmpPt, true);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.C:\n        d = cubicProjectPoint(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.Q:\n        d = quadraticProjectPoint(xi, yi, data[i++], data[i++], data[i], data[i + 1], x, y, tmpPt);\n        xi = data[i++];\n        yi = data[i++];\n        break;\n      case CMD.A:\n        // TODO Arc 判断的开销比较大\n        var cx = data[i++];\n        var cy = data[i++];\n        var rx = data[i++];\n        var ry = data[i++];\n        var theta = data[i++];\n        var dTheta = data[i++];\n        // TODO Arc 旋转\n        i += 1;\n        var anticlockwise = !!(1 - data[i++]);\n        x1 = Math.cos(theta) * rx + cx;\n        y1 = Math.sin(theta) * ry + cy;\n        // 不是直接使用 arc 命令\n        if (i <= 1) {\n          // 第一个命令起点还未定义\n          x0 = x1;\n          y0 = y1;\n        }\n        // zr 使用scale来模拟椭圆, 这里也对x做一定的缩放\n        var _x = (x - cx) * ry / rx + cx;\n        d = projectPointToArc(cx, cy, ry, theta, theta + dTheta, anticlockwise, _x, y, tmpPt);\n        xi = Math.cos(theta + dTheta) * rx + cx;\n        yi = Math.sin(theta + dTheta) * ry + cy;\n        break;\n      case CMD.R:\n        x0 = xi = data[i++];\n        y0 = yi = data[i++];\n        var width = data[i++];\n        var height = data[i++];\n        d = projectPointToRect(x0, y0, width, height, x, y, tmpPt);\n        break;\n      case CMD.Z:\n        d = projectPointToLine(xi, yi, x0, y0, x, y, tmpPt, true);\n        xi = x0;\n        yi = y0;\n        break;\n    }\n    if (d < minDist) {\n      minDist = d;\n      out.set(tmpPt[0], tmpPt[1]);\n    }\n  }\n  return minDist;\n}\n// Temporal variable for intermediate usage.\nvar pt0 = new Point();\nvar pt1 = new Point();\nvar pt2 = new Point();\nvar dir = new Point();\nvar dir2 = new Point();\n/**\r\n * Calculate a proper guide line based on the label position and graphic element definition\r\n * @param label\r\n * @param labelRect\r\n * @param target\r\n * @param targetRect\r\n */\nexport function updateLabelLinePoints(target, labelLineModel) {\n  if (!target) {\n    return;\n  }\n  var labelLine = target.getTextGuideLine();\n  var label = target.getTextContent();\n  // Needs to create text guide in each charts.\n  if (!(label && labelLine)) {\n    return;\n  }\n  var labelGuideConfig = target.textGuideLineConfig || {};\n  var points = [[0, 0], [0, 0], [0, 0]];\n  var searchSpace = labelGuideConfig.candidates || DEFAULT_SEARCH_SPACE;\n  var labelRect = label.getBoundingRect().clone();\n  labelRect.applyTransform(label.getComputedTransform());\n  var minDist = Infinity;\n  var anchorPoint = labelGuideConfig.anchor;\n  var targetTransform = target.getComputedTransform();\n  var targetInversedTransform = targetTransform && invert([], targetTransform);\n  var len = labelLineModel.get('length2') || 0;\n  if (anchorPoint) {\n    pt2.copy(anchorPoint);\n  }\n  for (var i = 0; i < searchSpace.length; i++) {\n    var candidate = searchSpace[i];\n    getCandidateAnchor(candidate, 0, labelRect, pt0, dir);\n    Point.scaleAndAdd(pt1, pt0, dir, len);\n    // Transform to target coord space.\n    pt1.transform(targetInversedTransform);\n    // Note: getBoundingRect will ensure the `path` being created.\n    var boundingRect = target.getBoundingRect();\n    var dist = anchorPoint ? anchorPoint.distance(pt1) : target instanceof Path ? nearestPointOnPath(pt1, target.path, pt2) : nearestPointOnRect(pt1, boundingRect, pt2);\n    // TODO pt2 is in the path\n    if (dist < minDist) {\n      minDist = dist;\n      // Transform back to global space.\n      pt1.transform(targetTransform);\n      pt2.transform(targetTransform);\n      pt2.toArray(points[0]);\n      pt1.toArray(points[1]);\n      pt0.toArray(points[2]);\n    }\n  }\n  limitTurnAngle(points, labelLineModel.get('minTurnAngle'));\n  labelLine.setShape({\n    points: points\n  });\n}\n// Temporal variable for the limitTurnAngle function\nvar tmpArr = [];\nvar tmpProjPoint = new Point();\n/**\r\n * Reduce the line segment attached to the label to limit the turn angle between two segments.\r\n * @param linePoints\r\n * @param minTurnAngle Radian of minimum turn angle. 0 - 180\r\n */\nexport function limitTurnAngle(linePoints, minTurnAngle) {\n  if (!(minTurnAngle <= 180 && minTurnAngle > 0)) {\n    return;\n  }\n  minTurnAngle = minTurnAngle / 180 * Math.PI;\n  // The line points can be\n  //      /pt1----pt2 (label)\n  //     /\n  // pt0/\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt0, pt1);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(dir2);\n  var minTurnAngleCos = Math.cos(minTurnAngle);\n  if (minTurnAngleCos < angleCos) {\n    // Smaller than minTurnAngle\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    // Calculate new projected length with limited minTurnAngle and get the new connect point\n    tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI - minTurnAngle));\n    // Limit the new calculated connect point between pt1 and pt2.\n    var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n    if (isNaN(t)) {\n      return;\n    }\n    if (t < 0) {\n      Point.copy(tmpProjPoint, pt1);\n    } else if (t > 1) {\n      Point.copy(tmpProjPoint, pt2);\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\n/**\r\n * Limit the angle of line and the surface\r\n * @param maxSurfaceAngle Radian of minimum turn angle. 0 - 180. 0 is same direction to normal. 180 is opposite\r\n */\nexport function limitSurfaceAngle(linePoints, surfaceNormal, maxSurfaceAngle) {\n  if (!(maxSurfaceAngle <= 180 && maxSurfaceAngle > 0)) {\n    return;\n  }\n  maxSurfaceAngle = maxSurfaceAngle / 180 * Math.PI;\n  pt0.fromArray(linePoints[0]);\n  pt1.fromArray(linePoints[1]);\n  pt2.fromArray(linePoints[2]);\n  Point.sub(dir, pt1, pt0);\n  Point.sub(dir2, pt2, pt1);\n  var len1 = dir.len();\n  var len2 = dir2.len();\n  if (len1 < 1e-3 || len2 < 1e-3) {\n    return;\n  }\n  dir.scale(1 / len1);\n  dir2.scale(1 / len2);\n  var angleCos = dir.dot(surfaceNormal);\n  var maxSurfaceAngleCos = Math.cos(maxSurfaceAngle);\n  if (angleCos < maxSurfaceAngleCos) {\n    // Calculate project point of pt0 on pt1-pt2\n    var d = projectPointToLine(pt1.x, pt1.y, pt2.x, pt2.y, pt0.x, pt0.y, tmpArr, false);\n    tmpProjPoint.fromArray(tmpArr);\n    var HALF_PI = Math.PI / 2;\n    var angle2 = Math.acos(dir2.dot(surfaceNormal));\n    var newAngle = HALF_PI + angle2 - maxSurfaceAngle;\n    if (newAngle >= HALF_PI) {\n      // parallel\n      Point.copy(tmpProjPoint, pt2);\n    } else {\n      // Calculate new projected length with limited minTurnAngle and get the new connect point\n      tmpProjPoint.scaleAndAdd(dir2, d / Math.tan(Math.PI / 2 - newAngle));\n      // Limit the new calculated connect point between pt1 and pt2.\n      var t = pt2.x !== pt1.x ? (tmpProjPoint.x - pt1.x) / (pt2.x - pt1.x) : (tmpProjPoint.y - pt1.y) / (pt2.y - pt1.y);\n      if (isNaN(t)) {\n        return;\n      }\n      if (t < 0) {\n        Point.copy(tmpProjPoint, pt1);\n      } else if (t > 1) {\n        Point.copy(tmpProjPoint, pt2);\n      }\n    }\n    tmpProjPoint.toArray(linePoints[1]);\n  }\n}\nfunction setLabelLineState(labelLine, ignore, stateName, stateModel) {\n  var isNormal = stateName === 'normal';\n  var stateObj = isNormal ? labelLine : labelLine.ensureState(stateName);\n  // Make sure display.\n  stateObj.ignore = ignore;\n  // Set smooth\n  var smooth = stateModel.get('smooth');\n  if (smooth && smooth === true) {\n    smooth = 0.3;\n  }\n  stateObj.shape = stateObj.shape || {};\n  if (smooth > 0) {\n    stateObj.shape.smooth = smooth;\n  }\n  var styleObj = stateModel.getModel('lineStyle').getLineStyle();\n  isNormal ? labelLine.useStyle(styleObj) : stateObj.style = styleObj;\n}\nfunction buildLabelLinePath(path, shape) {\n  var smooth = shape.smooth;\n  var points = shape.points;\n  if (!points) {\n    return;\n  }\n  path.moveTo(points[0][0], points[0][1]);\n  if (smooth > 0 && points.length >= 3) {\n    var len1 = vector.dist(points[0], points[1]);\n    var len2 = vector.dist(points[1], points[2]);\n    if (!len1 || !len2) {\n      path.lineTo(points[1][0], points[1][1]);\n      path.lineTo(points[2][0], points[2][1]);\n      return;\n    }\n    var moveLen = Math.min(len1, len2) * smooth;\n    var midPoint0 = vector.lerp([], points[1], points[0], moveLen / len1);\n    var midPoint2 = vector.lerp([], points[1], points[2], moveLen / len2);\n    var midPoint1 = vector.lerp([], midPoint0, midPoint2, 0.5);\n    path.bezierCurveTo(midPoint0[0], midPoint0[1], midPoint0[0], midPoint0[1], midPoint1[0], midPoint1[1]);\n    path.bezierCurveTo(midPoint2[0], midPoint2[1], midPoint2[0], midPoint2[1], points[2][0], points[2][1]);\n  } else {\n    for (var i = 1; i < points.length; i++) {\n      path.lineTo(points[i][0], points[i][1]);\n    }\n  }\n}\n/**\r\n * Create a label line if necessary and set it's style.\r\n */\nexport function setLabelLineStyle(targetEl, statesModels, defaultStyle) {\n  var labelLine = targetEl.getTextGuideLine();\n  var label = targetEl.getTextContent();\n  if (!label) {\n    // Not show label line if there is no label.\n    if (labelLine) {\n      targetEl.removeTextGuideLine();\n    }\n    return;\n  }\n  var normalModel = statesModels.normal;\n  var showNormal = normalModel.get('show');\n  var labelIgnoreNormal = label.ignore;\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateName = DISPLAY_STATES[i];\n    var stateModel = statesModels[stateName];\n    var isNormal = stateName === 'normal';\n    if (stateModel) {\n      var stateShow = stateModel.get('show');\n      var isLabelIgnored = isNormal ? labelIgnoreNormal : retrieve2(label.states[stateName] && label.states[stateName].ignore, labelIgnoreNormal);\n      if (isLabelIgnored // Not show when label is not shown in this state.\n      || !retrieve2(stateShow, showNormal) // Use normal state by default if not set.\n      ) {\n        var stateObj = isNormal ? labelLine : labelLine && labelLine.states[stateName];\n        if (stateObj) {\n          stateObj.ignore = true;\n        }\n        if (!!labelLine) {\n          setLabelLineState(labelLine, true, stateName, stateModel);\n        }\n        continue;\n      }\n      // Create labelLine if not exists\n      if (!labelLine) {\n        labelLine = new Polyline();\n        targetEl.setTextGuideLine(labelLine);\n        // Reset state of normal because it's new created.\n        // NOTE: NORMAL should always been the first!\n        if (!isNormal && (labelIgnoreNormal || !showNormal)) {\n          setLabelLineState(labelLine, true, 'normal', statesModels.normal);\n        }\n        // Use same state proxy.\n        if (targetEl.stateProxy) {\n          labelLine.stateProxy = targetEl.stateProxy;\n        }\n      }\n      setLabelLineState(labelLine, false, stateName, stateModel);\n    }\n  }\n  if (labelLine) {\n    defaults(labelLine.style, defaultStyle);\n    // Not fill.\n    labelLine.style.fill = null;\n    var showAbove = normalModel.get('showAbove');\n    var labelLineConfig = targetEl.textGuideLineConfig = targetEl.textGuideLineConfig || {};\n    labelLineConfig.showAbove = showAbove || false;\n    // Custom the buildPath.\n    labelLine.buildPath = buildLabelLinePath;\n  }\n}\nexport function getLabelLineStatesModels(itemModel, labelLineName) {\n  labelLineName = labelLineName || 'labelLine';\n  var statesModels = {\n    normal: itemModel.getModel(labelLineName)\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelLineName]);\n  }\n  return statesModels;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAmDA,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,MAAM,kBAAU;AACpB,IAAI,uBAAuB,CAAC,OAAO,SAAS,UAAU,MAAM;AAC5D,SAAS,mBAAmB,KAAK,UAAU,MAAM,OAAO,QAAQ;AAC9D,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,QAAQ,GAAG,KAAK,IAAI,QAAQ;AAC/C,aAAO,IAAI,GAAG,EAAE;AAChB;AAAA,IACF,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,QAAQ,GAAG,KAAK,IAAI,SAAS,QAAQ;AACxD,aAAO,IAAI,GAAG,CAAC;AACf;AAAA,IACF,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,UAAU,KAAK,IAAI,SAAS,CAAC;AAChD,aAAO,IAAI,IAAI,CAAC;AAChB;AAAA,IACF,KAAK;AACH,YAAM,IAAI,KAAK,IAAI,QAAQ,UAAU,KAAK,IAAI,SAAS,CAAC;AACxD,aAAO,IAAI,GAAG,CAAC;AACf;AAAA,EACJ;AACF;AACA,SAAS,kBAAkB,IAAI,IAAI,GAAG,YAAY,UAAU,eAAe,GAAG,GAAG,KAAK;AACpF,OAAK;AACL,OAAK;AACL,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,OAAK;AACL,OAAK;AAEL,MAAI,KAAK,IAAI,IAAI;AACjB,MAAI,KAAK,IAAI,IAAI;AACjB,MAAI,KAAK,IAAI,aAAa,QAAQ,IAAI,MAAM,MAAM;AAEhD,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,IAAI;AAAA,EACb;AACA,MAAI,eAAe;AACjB,QAAI,MAAM;AACV,iBAAa,gBAAgB,QAAQ;AACrC,eAAW,gBAAgB,GAAG;AAAA,EAChC,OAAO;AACL,iBAAa,gBAAgB,UAAU;AACvC,eAAW,gBAAgB,QAAQ;AAAA,EACrC;AACA,MAAI,aAAa,UAAU;AACzB,gBAAY;AAAA,EACd;AACA,MAAI,QAAQ,KAAK,MAAM,GAAG,CAAC;AAC3B,MAAI,QAAQ,GAAG;AACb,aAAS;AAAA,EACX;AACA,MAAI,SAAS,cAAc,SAAS,YAAY,QAAQ,OAAO,cAAc,QAAQ,OAAO,UAAU;AAEpG,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,IAAI;AAAA,EACb;AACA,MAAI,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI;AACpC,MAAI,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI;AACpC,MAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI;AAClC,MAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI;AAClC,MAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAChD,MAAI,KAAK,IAAI;AACX,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,KAAK,KAAK,EAAE;AAAA,EACrB,OAAO;AACL,QAAI,CAAC,IAAI;AACT,QAAI,CAAC,IAAI;AACT,WAAO,KAAK,KAAK,EAAE;AAAA,EACrB;AACF;AACA,SAAS,mBAAmB,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,aAAa;AAClE,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,KAAK;AACf,MAAI,UAAU,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC7C,SAAO;AACP,SAAO;AAEP,MAAI,eAAe,KAAK,MAAM,KAAK;AACnC,MAAI,IAAI,eAAe;AACvB,MAAI,aAAa;AACf,QAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,EAChC;AACA,OAAK;AACL,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;AAC3B,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;AAC3B,SAAO,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,EAAE;AAC5D;AACA,SAAS,mBAAmB,IAAI,IAAI,OAAO,QAAQ,GAAG,GAAG,KAAK;AAC5D,MAAI,QAAQ,GAAG;AACb,SAAK,KAAK;AACV,YAAQ,CAAC;AAAA,EACX;AACA,MAAI,SAAS,GAAG;AACd,SAAK,KAAK;AACV,aAAS,CAAC;AAAA,EACZ;AACA,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,KAAK;AACd,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AAC9C,MAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AAC9C,SAAO,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,EAAE;AAC5D;AACA,IAAI,QAAQ,CAAC;AACb,SAAS,mBAAmB,IAAI,MAAM,KAAK;AACzC,MAAIA,QAAO,mBAAmB,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,KAAK;AACxF,MAAI,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAC1B,SAAOA;AACT;AAKA,SAAS,mBAAmB,IAAI,MAAM,KAAK;AACzC,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,OAAO,KAAK;AAChB,MAAI,IAAI,GAAG;AACX,MAAI,IAAI,GAAG;AACX,WAAS,IAAI,GAAG,IAAI,KAAK,UAAS;AAChC,QAAI,MAAM,KAAK,GAAG;AAClB,QAAI,MAAM,GAAG;AACX,WAAK,KAAK,CAAC;AACX,WAAK,KAAK,IAAI,CAAC;AACf,WAAK;AACL,WAAK;AAAA,IACP;AACA,QAAI,IAAI;AACR,YAAQ,KAAK;AAAA,MACX,KAAK,IAAI;AAGP,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb,aAAK;AACL,aAAK;AACL;AAAA,MACF,KAAK,IAAI;AACP,YAAI,mBAAmB,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,OAAO,IAAI;AACtE,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAK,IAAI;AACP,YAAI,kBAAkB,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK;AAC3G,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAK,IAAI;AACP,YAAI,sBAAsB,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,KAAK;AACzF,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AACb;AAAA,MACF,KAAK,IAAI;AAEP,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,KAAK,KAAK,GAAG;AACjB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AAErB,aAAK;AACL,YAAI,gBAAgB,CAAC,EAAE,IAAI,KAAK,GAAG;AACnC,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAC5B,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK;AAE5B,YAAI,KAAK,GAAG;AAEV,eAAK;AACL,eAAK;AAAA,QACP;AAEA,YAAI,MAAM,IAAI,MAAM,KAAK,KAAK;AAC9B,YAAI,kBAAkB,IAAI,IAAI,IAAI,OAAO,QAAQ,QAAQ,eAAe,IAAI,GAAG,KAAK;AACpF,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC,aAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,KAAK;AACrC;AAAA,MACF,KAAK,IAAI;AACP,aAAK,KAAK,KAAK,GAAG;AAClB,aAAK,KAAK,KAAK,GAAG;AAClB,YAAI,QAAQ,KAAK,GAAG;AACpB,YAAI,SAAS,KAAK,GAAG;AACrB,YAAI,mBAAmB,IAAI,IAAI,OAAO,QAAQ,GAAG,GAAG,KAAK;AACzD;AAAA,MACF,KAAK,IAAI;AACP,YAAI,mBAAmB,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,OAAO,IAAI;AACxD,aAAK;AACL,aAAK;AACL;AAAA,IACJ;AACA,QAAI,IAAI,SAAS;AACf,gBAAU;AACV,UAAI,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,IAC5B;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,MAAM,IAAI,cAAM;AACpB,IAAI,OAAO,IAAI,cAAM;AAQd,SAAS,sBAAsB,QAAQ,gBAAgB;AAC5D,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,MAAI,YAAY,OAAO,iBAAiB;AACxC,MAAI,QAAQ,OAAO,eAAe;AAElC,MAAI,EAAE,SAAS,YAAY;AACzB;AAAA,EACF;AACA,MAAI,mBAAmB,OAAO,uBAAuB,CAAC;AACtD,MAAI,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC,MAAI,cAAc,iBAAiB,cAAc;AACjD,MAAI,YAAY,MAAM,gBAAgB,EAAE,MAAM;AAC9C,YAAU,eAAe,MAAM,qBAAqB,CAAC;AACrD,MAAI,UAAU;AACd,MAAI,cAAc,iBAAiB;AACnC,MAAI,kBAAkB,OAAO,qBAAqB;AAClD,MAAI,0BAA0B,mBAAmB,OAAO,CAAC,GAAG,eAAe;AAC3E,MAAI,MAAM,eAAe,IAAI,SAAS,KAAK;AAC3C,MAAI,aAAa;AACf,QAAI,KAAK,WAAW;AAAA,EACtB;AACA,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,QAAI,YAAY,YAAY,CAAC;AAC7B,uBAAmB,WAAW,GAAG,WAAW,KAAK,GAAG;AACpD,kBAAM,YAAY,KAAK,KAAK,KAAK,GAAG;AAEpC,QAAI,UAAU,uBAAuB;AAErC,QAAI,eAAe,OAAO,gBAAgB;AAC1C,QAAIA,QAAO,cAAc,YAAY,SAAS,GAAG,IAAI,kBAAkB,eAAO,mBAAmB,KAAK,OAAO,MAAM,GAAG,IAAI,mBAAmB,KAAK,cAAc,GAAG;AAEnK,QAAIA,QAAO,SAAS;AAClB,gBAAUA;AAEV,UAAI,UAAU,eAAe;AAC7B,UAAI,UAAU,eAAe;AAC7B,UAAI,QAAQ,OAAO,CAAC,CAAC;AACrB,UAAI,QAAQ,OAAO,CAAC,CAAC;AACrB,UAAI,QAAQ,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,EACF;AACA,iBAAe,QAAQ,eAAe,IAAI,cAAc,CAAC;AACzD,YAAU,SAAS;AAAA,IACjB;AAAA,EACF,CAAC;AACH;AAEA,IAAI,SAAS,CAAC;AACd,IAAI,eAAe,IAAI,cAAM;AAMtB,SAAS,eAAe,YAAY,cAAc;AACvD,MAAI,EAAE,gBAAgB,OAAO,eAAe,IAAI;AAC9C;AAAA,EACF;AACA,iBAAe,eAAe,MAAM,KAAK;AAKzC,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,gBAAM,IAAI,KAAK,KAAK,GAAG;AACvB,gBAAM,IAAI,MAAM,KAAK,GAAG;AACxB,MAAI,OAAO,IAAI,IAAI;AACnB,MAAI,OAAO,KAAK,IAAI;AACpB,MAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B;AAAA,EACF;AACA,MAAI,MAAM,IAAI,IAAI;AAClB,OAAK,MAAM,IAAI,IAAI;AACnB,MAAI,WAAW,IAAI,IAAI,IAAI;AAC3B,MAAI,kBAAkB,KAAK,IAAI,YAAY;AAC3C,MAAI,kBAAkB,UAAU;AAG9B,QAAI,IAAI,mBAAmB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClF,iBAAa,UAAU,MAAM;AAE7B,iBAAa,YAAY,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,YAAY,CAAC;AAEnE,QAAI,IAAI,IAAI,MAAM,IAAI,KAAK,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI;AAC/G,QAAI,MAAM,CAAC,GAAG;AACZ;AAAA,IACF;AACA,QAAI,IAAI,GAAG;AACT,oBAAM,KAAK,cAAc,GAAG;AAAA,IAC9B,WAAW,IAAI,GAAG;AAChB,oBAAM,KAAK,cAAc,GAAG;AAAA,IAC9B;AACA,iBAAa,QAAQ,WAAW,CAAC,CAAC;AAAA,EACpC;AACF;AAKO,SAAS,kBAAkB,YAAY,eAAe,iBAAiB;AAC5E,MAAI,EAAE,mBAAmB,OAAO,kBAAkB,IAAI;AACpD;AAAA,EACF;AACA,oBAAkB,kBAAkB,MAAM,KAAK;AAC/C,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,MAAI,UAAU,WAAW,CAAC,CAAC;AAC3B,gBAAM,IAAI,KAAK,KAAK,GAAG;AACvB,gBAAM,IAAI,MAAM,KAAK,GAAG;AACxB,MAAI,OAAO,IAAI,IAAI;AACnB,MAAI,OAAO,KAAK,IAAI;AACpB,MAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B;AAAA,EACF;AACA,MAAI,MAAM,IAAI,IAAI;AAClB,OAAK,MAAM,IAAI,IAAI;AACnB,MAAI,WAAW,IAAI,IAAI,aAAa;AACpC,MAAI,qBAAqB,KAAK,IAAI,eAAe;AACjD,MAAI,WAAW,oBAAoB;AAEjC,QAAI,IAAI,mBAAmB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClF,iBAAa,UAAU,MAAM;AAC7B,QAAI,UAAU,KAAK,KAAK;AACxB,QAAI,SAAS,KAAK,KAAK,KAAK,IAAI,aAAa,CAAC;AAC9C,QAAI,WAAW,UAAU,SAAS;AAClC,QAAI,YAAY,SAAS;AAEvB,oBAAM,KAAK,cAAc,GAAG;AAAA,IAC9B,OAAO;AAEL,mBAAa,YAAY,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,QAAQ,CAAC;AAEnE,UAAI,IAAI,IAAI,MAAM,IAAI,KAAK,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,aAAa,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI;AAC/G,UAAI,MAAM,CAAC,GAAG;AACZ;AAAA,MACF;AACA,UAAI,IAAI,GAAG;AACT,sBAAM,KAAK,cAAc,GAAG;AAAA,MAC9B,WAAW,IAAI,GAAG;AAChB,sBAAM,KAAK,cAAc,GAAG;AAAA,MAC9B;AAAA,IACF;AACA,iBAAa,QAAQ,WAAW,CAAC,CAAC;AAAA,EACpC;AACF;AACA,SAAS,kBAAkB,WAAW,QAAQ,WAAW,YAAY;AACnE,MAAI,WAAW,cAAc;AAC7B,MAAI,WAAW,WAAW,YAAY,UAAU,YAAY,SAAS;AAErE,WAAS,SAAS;AAElB,MAAI,SAAS,WAAW,IAAI,QAAQ;AACpC,MAAI,UAAU,WAAW,MAAM;AAC7B,aAAS;AAAA,EACX;AACA,WAAS,QAAQ,SAAS,SAAS,CAAC;AACpC,MAAI,SAAS,GAAG;AACd,aAAS,MAAM,SAAS;AAAA,EAC1B;AACA,MAAI,WAAW,WAAW,SAAS,WAAW,EAAE,aAAa;AAC7D,aAAW,UAAU,SAAS,QAAQ,IAAI,SAAS,QAAQ;AAC7D;AACA,SAAS,mBAAmB,MAAM,OAAO;AACvC,MAAI,SAAS,MAAM;AACnB,MAAI,SAAS,MAAM;AACnB,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,OAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AACtC,MAAI,SAAS,KAAK,OAAO,UAAU,GAAG;AACpC,QAAI,OAAc,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3C,QAAI,OAAc,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3C,QAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,WAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AACtC,WAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AACtC;AAAA,IACF;AACA,QAAI,UAAU,KAAK,IAAI,MAAM,IAAI,IAAI;AACrC,QAAI,YAAmB,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,UAAU,IAAI;AACpE,QAAI,YAAmB,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,UAAU,IAAI;AACpE,QAAI,YAAmB,KAAK,CAAC,GAAG,WAAW,WAAW,GAAG;AACzD,SAAK,cAAc,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AACrG,SAAK,cAAc,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,EACvG,OAAO;AACL,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAK,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,IACxC;AAAA,EACF;AACF;AAIO,SAAS,kBAAkB,UAAU,cAAc,cAAc;AACtE,MAAI,YAAY,SAAS,iBAAiB;AAC1C,MAAI,QAAQ,SAAS,eAAe;AACpC,MAAI,CAAC,OAAO;AAEV,QAAI,WAAW;AACb,eAAS,oBAAoB;AAAA,IAC/B;AACA;AAAA,EACF;AACA,MAAI,cAAc,aAAa;AAC/B,MAAI,aAAa,YAAY,IAAI,MAAM;AACvC,MAAI,oBAAoB,MAAM;AAC9B,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,QAAI,YAAY,eAAe,CAAC;AAChC,QAAI,aAAa,aAAa,SAAS;AACvC,QAAI,WAAW,cAAc;AAC7B,QAAI,YAAY;AACd,UAAI,YAAY,WAAW,IAAI,MAAM;AACrC,UAAI,iBAAiB,WAAW,oBAAoB,UAAU,MAAM,OAAO,SAAS,KAAK,MAAM,OAAO,SAAS,EAAE,QAAQ,iBAAiB;AAC1I,UAAI,kBACD,CAAC,UAAU,WAAW,UAAU,GACjC;AACA,YAAI,WAAW,WAAW,YAAY,aAAa,UAAU,OAAO,SAAS;AAC7E,YAAI,UAAU;AACZ,mBAAS,SAAS;AAAA,QACpB;AACA,YAAI,CAAC,CAAC,WAAW;AACf,4BAAkB,WAAW,MAAM,WAAW,UAAU;AAAA,QAC1D;AACA;AAAA,MACF;AAEA,UAAI,CAAC,WAAW;AACd,oBAAY,IAAI,iBAAS;AACzB,iBAAS,iBAAiB,SAAS;AAGnC,YAAI,CAAC,aAAa,qBAAqB,CAAC,aAAa;AACnD,4BAAkB,WAAW,MAAM,UAAU,aAAa,MAAM;AAAA,QAClE;AAEA,YAAI,SAAS,YAAY;AACvB,oBAAU,aAAa,SAAS;AAAA,QAClC;AAAA,MACF;AACA,wBAAkB,WAAW,OAAO,WAAW,UAAU;AAAA,IAC3D;AAAA,EACF;AACA,MAAI,WAAW;AACb,aAAS,UAAU,OAAO,YAAY;AAEtC,cAAU,MAAM,OAAO;AACvB,QAAI,YAAY,YAAY,IAAI,WAAW;AAC3C,QAAI,kBAAkB,SAAS,sBAAsB,SAAS,uBAAuB,CAAC;AACtF,oBAAgB,YAAY,aAAa;AAEzC,cAAU,YAAY;AAAA,EACxB;AACF;AACO,SAAS,yBAAyB,WAAW,eAAe;AACjE,kBAAgB,iBAAiB;AACjC,MAAI,eAAe;AAAA,IACjB,QAAQ,UAAU,SAAS,aAAa;AAAA,EAC1C;AACA,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,QAAI,YAAY,eAAe,CAAC;AAChC,iBAAa,SAAS,IAAI,UAAU,SAAS,CAAC,WAAW,aAAa,CAAC;AAAA,EACzE;AACA,SAAO;AACT;", "names": ["dist"]}