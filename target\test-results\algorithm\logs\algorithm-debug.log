2025-08-19 17:47:01.647 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-19 17:47:01.649 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-19 17:47:01.649 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-19 17:47:01.649 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-19 17:47:01.649 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-19 17:47:01.650 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-19 17:47:01.650 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-19 17:47:01.650 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-19 17:47:01.650 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-19 17:47:01.666 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-19 17:47:01.667 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-19 17:47:01.668 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-19 17:47:01.668 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-19 17:47:01.668 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-19 17:47:01.668 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-19 17:47:01.668 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-19 17:47:01.668 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-19 17:47:01.675 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.676 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.677 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-19 17:47:01.677 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-19 17:47:01.685 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:47:01.685 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:47:01.686 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-19 17:47:01.686 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-19 17:47:01.831 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.841 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-19 17:47:01.842 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.842 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.872 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-19 17:47:01.881 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-19 17:47:01.882 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-19 17:47:01.882 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.891 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.909 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:01.909 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:01.909 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:01.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.909 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.911 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:01.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.911 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.913 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:01.913 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:01.913 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.919 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:01.919 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.921 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:01.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.921 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.922 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.922 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.922 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:01.922 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:01.922 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:01.922 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:01.922 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:01.922 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:01.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.923 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.925 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:01.925 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.927 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:01.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:01.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:01.927 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:01.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:01.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:01.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:01.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:01.928 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:01.928 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:01.928 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:01.928 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:01.928 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:01.929 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:01.934 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-19 17:47:01.970 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-19 17:47:02.041 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-19 17:47:02.048 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.048 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.048 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.048 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.048 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.049 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.049 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.050 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.050 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.050 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.050 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.050 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.050 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.050 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.050 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.051 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.051 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.051 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.053 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.053 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.058 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.058 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.058 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.058 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.058 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.061 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.061 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.063 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.063 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.063 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.063 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.063 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.063 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.063 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.063 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.065 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.066 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.066 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.066 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.066 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.066 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:02.066 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:02.066 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:02.068 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.069 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.069 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.069 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.069 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.071 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.071 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.071 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.071 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.071 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.079 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.079 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.079 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.079 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.079 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.081 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.081 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.081 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.081 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.081 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.087 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.087 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.087 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.087 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.087 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.089 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.089 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.090 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.090 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.090 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.090 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.090 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.090 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.090 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.090 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.092 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.092 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.092 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.092 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.092 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.092 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.092 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.092 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.095 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.096 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.096 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.096 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.096 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.096 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:02.096 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:02.096 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:02.098 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.098 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.098 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.098 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.098 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.102 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.103 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.106 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.108 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.108 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.110 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.110 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.110 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.114 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-19 17:47:02.619 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-19 17:47:02.619 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-19 17:47:02.622 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.622 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.622 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.622 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.622 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.624 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.624 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.624 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.626 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.626 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.626 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.626 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.626 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.626 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.626 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.626 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.628 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.628 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.628 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.628 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.628 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.628 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:02.628 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:02.628 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:02.631 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-19 17:47:02.631 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-19 17:47:02.631 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-19 17:47:02.631 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-19 17:47:02.631 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-19 17:47:02.632 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-19 17:47:02.832 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-19 17:47:02.832 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-19 17:47:02.832 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-19 17:47:02.832 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-19 17:47:02.832 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-19 17:47:02.832 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-19 17:47:02.833 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-19 17:47:02.834 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-19 17:47:02.835 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-19 17:47:02.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.838 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.839 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.839 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.839 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.839 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-19 17:47:02.846 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-19 17:47:02.846 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-19 17:47:02.849 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.849 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.849 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.849 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.849 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.851 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.851 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.853 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.853 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.855 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.855 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.855 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.857 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.857 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.860 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.860 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.860 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.860 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:02.860 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:02.860 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:02.862 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.862 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.862 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.862 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.862 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.864 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.864 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.866 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.866 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.866 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.867 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.868 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.868 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.868 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.869 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.870 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.870 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.870 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.873 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.873 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.873 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.873 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.873 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.873 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.873 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.873 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.875 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.875 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.875 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.875 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.875 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.875 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:02.875 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:02.875 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:02.878 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.878 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.878 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.878 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.878 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.880 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.880 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.882 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.882 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.882 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:02.882 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:02.882 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:02.882 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:02.882 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:02.882 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:02.883 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:02.883 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:02.883 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:02.883 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:02.883 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:02.883 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:02.883 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:02.883 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-19 17:47:21.080 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-19 17:47:21.080 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-19 17:47:21.090 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-19 17:47:21.090 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-19 17:47:21.091 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-19 17:47:21.091 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-19 17:47:21.091 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-19 17:47:21.091 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-19 17:47:21.091 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-19 17:47:21.091 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-19 17:47:21.094 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.094 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.094 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-19 17:47:21.095 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-19 17:47:21.096 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:47:21.097 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:47:21.097 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-19 17:47:21.097 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-19 17:47:21.200 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.200 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-19 17:47:21.200 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.200 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.233 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-19 17:47:21.240 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-19 17:47:21.240 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-19 17:47:21.240 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.245 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.259 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.259 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.261 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.261 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.261 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.261 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.261 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.261 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.261 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.263 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.263 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.263 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.265 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.265 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.265 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.266 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.267 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.267 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.268 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.268 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.269 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.269 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.271 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.271 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.271 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.271 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.272 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.272 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.273 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.273 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.273 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.273 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.273 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.273 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.273 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.273 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.276 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-19 17:47:21.305 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-19 17:47:21.348 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-19 17:47:21.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.351 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.351 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.351 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.351 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.352 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.352 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.353 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.353 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.354 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.354 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.359 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.360 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.360 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.361 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.361 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.361 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.361 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.361 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.361 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.361 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.361 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.363 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.363 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.363 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.363 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.363 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.363 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.363 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.363 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.364 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.365 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.366 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.366 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.366 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.366 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.369 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.370 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.370 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.370 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.373 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.373 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.373 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.373 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.373 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.374 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.374 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.375 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.375 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.376 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.376 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.376 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.376 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.376 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.376 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.377 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.378 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.378 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.379 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.379 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.379 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.379 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.379 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.380 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.380 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.381 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.383 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-19 17:47:21.768 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-19 17:47:21.768 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-19 17:47:21.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.770 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.771 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.771 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.771 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.772 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.772 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.773 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.773 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.773 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.773 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.773 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.773 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.773 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.773 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.774 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-19 17:47:21.775 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-19 17:47:21.775 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-19 17:47:21.775 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-19 17:47:21.775 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-19 17:47:21.775 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-19 17:47:21.950 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-19 17:47:21.950 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-19 17:47:21.951 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-19 17:47:21.951 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-19 17:47:21.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.953 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.954 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.954 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.954 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.954 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-19 17:47:21.957 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-19 17:47:21.957 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-19 17:47:21.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.959 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.960 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.960 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.961 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.961 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.961 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.961 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.961 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.962 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.962 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.963 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.963 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.964 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.964 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.964 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.964 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.964 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.964 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.964 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.964 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.965 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.966 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.966 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.966 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.967 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.967 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.968 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.968 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.968 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.968 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.968 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.968 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.969 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.969 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.969 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.969 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.969 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.970 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.970 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.971 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.971 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.972 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.972 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.972 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.972 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.972 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.972 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:21.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.974 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.975 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.975 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.975 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.976 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.976 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.976 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.976 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.976 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.976 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.976 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.977 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:21.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:21.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:21.977 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:21.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:21.977 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:21.978 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:21.978 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:21.978 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:21.978 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:21.978 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:21.978 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:21.978 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:21.978 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-19 17:47:55.726 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-19 17:47:55.726 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-19 17:47:55.736 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-19 17:47:55.737 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-19 17:47:55.737 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-19 17:47:55.737 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-19 17:47:55.737 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-19 17:47:55.737 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-19 17:47:55.737 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-19 17:47:55.737 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-19 17:47:55.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.741 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-19 17:47:55.741 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-19 17:47:55.743 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:47:55.743 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:47:55.744 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-19 17:47:55.745 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-19 17:47:55.842 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.842 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-19 17:47:55.842 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.842 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.887 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-19 17:47:55.894 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-19 17:47:55.895 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-19 17:47:55.895 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.919 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:55.919 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.920 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:55.920 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.920 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.920 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.920 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.920 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.921 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:55.921 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:55.921 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:55.924 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:55.924 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:55.924 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:55.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.925 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.926 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:55.926 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.927 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:55.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.927 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.928 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:55.928 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:55.928 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:55.928 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:55.928 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:55.928 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:55.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.929 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.930 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:55.930 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.931 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:55.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.931 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:55.933 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:55.933 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:55.933 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:55.933 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:55.933 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:55.933 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:55.933 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:55.933 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:55.937 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-19 17:47:55.962 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-19 17:47:55.997 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-19 17:47:55.999 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:55.999 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:55.999 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:55.999 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:55.999 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.000 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.000 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.000 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.000 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.001 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.001 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.001 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.001 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.001 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.001 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.002 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.002 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.002 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.003 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.003 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.006 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.006 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.006 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.006 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.006 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.007 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.007 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.007 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.008 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.008 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.008 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.009 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.009 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.009 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.009 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.009 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.009 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:56.009 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:56.009 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:56.011 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.011 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.011 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.011 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.011 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.012 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.012 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.012 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.012 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.012 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.016 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.016 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.016 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.016 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.016 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.017 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.017 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.017 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.017 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.017 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.019 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.019 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.019 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.019 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.019 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.020 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.020 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.020 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.021 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.021 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.021 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.021 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.021 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.021 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.021 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.021 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.022 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.022 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.022 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.022 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.022 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.022 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:56.022 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:56.022 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:56.023 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.023 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.023 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.023 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.023 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.024 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.024 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.025 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.025 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.025 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.025 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.025 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.026 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.026 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.026 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.027 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.027 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.027 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.029 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-19 17:47:56.450 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-19 17:47:56.450 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-19 17:47:56.452 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.452 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.452 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.452 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.452 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.453 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.453 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.453 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.454 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.454 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.454 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.454 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.454 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.454 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.454 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.454 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.455 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.455 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.455 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.455 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.455 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.455 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:56.455 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:56.455 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:56.456 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-19 17:47:56.456 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-19 17:47:56.456 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-19 17:47:56.456 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-19 17:47:56.456 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-19 17:47:56.457 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-19 17:47:56.630 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-19 17:47:56.630 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-19 17:47:56.630 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-19 17:47:56.630 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-19 17:47:56.630 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-19 17:47:56.630 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-19 17:47:56.630 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-19 17:47:56.630 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-19 17:47:56.631 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-19 17:47:56.633 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.633 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.633 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.633 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.633 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.634 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.634 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.634 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.634 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.634 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.634 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-19 17:47:56.637 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-19 17:47:56.637 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-19 17:47:56.638 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.639 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.639 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.639 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.639 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.639 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.639 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.639 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.639 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.640 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.640 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.640 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.640 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.640 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.641 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.641 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.641 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.642 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.642 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.642 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.642 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.642 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.642 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.642 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.642 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.643 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.643 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.643 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.643 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.643 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.643 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:56.643 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:56.643 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:56.644 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.644 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.644 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.644 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.644 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.645 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.645 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.645 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.646 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.646 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.646 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.646 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.646 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.646 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.646 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.646 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.647 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.647 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.648 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.648 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.648 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.649 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.649 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.649 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.650 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.650 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.650 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.650 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.650 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.651 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.651 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.651 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.651 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.651 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.651 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:56.651 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:56.651 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:47:56.654 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.654 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.654 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.654 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.654 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.655 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.655 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.655 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.656 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.656 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.656 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:47:56.656 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:47:56.656 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:47:56.656 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:47:56.656 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:47:56.656 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:47:56.657 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:47:56.657 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:47:56.657 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:47:56.657 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:47:56.657 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:47:56.657 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:47:56.657 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:47:56.657 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:14.563 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-19 17:49:14.564 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-19 17:49:14.564 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-19 17:49:14.564 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-19 17:49:14.564 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-19 17:49:14.564 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-19 17:49:14.564 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-19 17:49:14.564 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-19 17:49:14.564 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-19 17:49:14.576 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-19 17:49:14.578 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-19 17:49:14.578 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-19 17:49:14.579 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-19 17:49:14.579 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-19 17:49:14.579 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-19 17:49:14.579 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-19 17:49:14.579 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-19 17:49:14.583 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.583 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.583 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-19 17:49:14.583 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-19 17:49:14.587 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:49:14.587 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 17:49:14.588 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-19 17:49:14.588 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-19 17:49:14.691 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.692 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-19 17:49:14.692 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.692 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.725 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-19 17:49:14.732 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-19 17:49:14.732 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-19 17:49:14.732 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.751 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.752 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.752 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.752 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.752 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.753 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:14.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.753 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.754 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.754 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.754 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.758 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:14.759 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:14.759 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:14.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.759 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.761 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.761 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.761 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.761 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.761 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.761 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.761 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.761 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.761 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.762 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:14.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.762 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.763 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.763 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.765 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.765 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.765 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.766 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:14.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.766 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.767 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.767 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.767 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.767 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.767 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.767 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:14.767 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:14.767 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:14.771 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-19 17:49:14.792 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-19 17:49:14.832 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-19 17:49:14.836 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.836 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.836 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.836 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.836 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.837 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.837 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.838 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.838 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.845 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.845 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.845 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.845 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.845 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.846 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.846 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.847 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.847 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.847 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:14.848 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.848 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.848 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.848 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.848 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.849 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.849 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.849 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.849 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.849 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.849 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:14.849 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:14.849 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:14.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.851 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.851 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.852 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.852 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.852 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.852 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.852 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.856 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.856 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.856 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.856 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.856 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.857 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.857 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.857 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.857 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.859 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.859 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.859 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.859 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.859 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.860 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.860 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.860 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.861 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.861 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.861 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:14.861 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.861 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.861 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.861 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.861 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.862 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.862 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.862 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.862 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.862 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.862 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:14.862 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:14.862 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:14.863 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.863 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.863 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.863 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.863 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.864 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.866 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.866 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.866 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.866 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.866 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.867 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:14.867 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:14.867 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:14.868 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:14.868 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:14.868 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:14.870 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-19 17:49:15.269 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-19 17:49:15.269 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-19 17:49:15.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.272 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.273 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.273 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.273 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.273 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.273 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.274 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.274 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.275 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.275 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.275 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.275 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.275 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.275 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:15.275 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:15.275 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:15.277 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-19 17:49:15.277 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-19 17:49:15.277 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-19 17:49:15.277 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-19 17:49:15.277 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-19 17:49:15.277 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-19 17:49:15.443 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-19 17:49:15.443 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-19 17:49:15.445 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-19 17:49:15.445 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-19 17:49:15.447 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.447 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.447 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.447 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.447 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.448 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.448 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.448 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.448 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.448 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.448 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-19 17:49:15.453 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-19 17:49:15.453 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-19 17:49:15.455 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.455 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.455 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.455 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.455 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.456 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.456 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.456 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.456 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.457 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.457 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.457 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.457 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.457 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.458 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.458 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.458 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.459 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.459 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.459 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:15.459 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.459 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.459 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.459 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.459 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.460 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.460 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.460 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.460 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.460 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.460 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:15.460 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:15.460 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:15.461 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.461 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.461 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.461 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.461 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.462 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.462 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.462 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.463 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.463 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.463 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:15.464 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.464 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.464 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.464 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.464 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.465 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.465 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.465 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.466 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.466 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.466 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.467 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.467 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.467 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:15.467 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.467 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.467 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.467 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.467 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.468 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.468 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.469 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.469 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.469 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.469 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:15.469 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:15.469 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 17:49:15.471 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.471 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.471 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.471 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.471 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.472 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.472 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.472 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.473 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.473 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.473 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 17:49:15.473 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 17:49:15.473 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 17:49:15.473 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 17:49:15.473 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 17:49:15.473 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 17:49:15.474 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 17:49:15.474 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 17:49:15.474 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 17:49:15.474 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 17:49:15.474 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 17:49:15.474 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 17:49:15.474 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 17:49:15.474 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:09.490 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-19 21:14:09.491 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-19 21:14:09.491 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-19 21:14:09.491 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-19 21:14:09.491 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-19 21:14:09.491 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-19 21:14:09.491 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-19 21:14:09.491 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-19 21:14:09.491 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-19 21:14:09.506 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-19 21:14:09.507 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-19 21:14:09.507 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-19 21:14:09.507 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-19 21:14:09.507 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-19 21:14:09.508 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-19 21:14:09.508 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-19 21:14:09.508 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-19 21:14:09.515 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.515 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.516 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-19 21:14:09.516 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-19 21:14:09.520 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 21:14:09.522 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 21:14:09.522 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-19 21:14:09.523 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-19 21:14:09.656 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.657 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-19 21:14:09.657 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.657 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.689 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-19 21:14:09.701 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-19 21:14:09.701 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-19 21:14:09.701 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.712 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.731 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.731 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.731 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.731 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.731 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.731 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.731 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.731 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.732 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.732 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.733 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.735 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.735 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.735 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.739 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:09.740 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:09.740 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.743 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.743 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.744 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.744 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.744 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:09.744 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.744 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.744 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.744 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.744 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.747 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.747 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.749 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.749 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.750 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:09.751 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.751 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.751 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.751 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.751 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.753 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.753 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.753 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.753 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:09.753 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:09.753 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:09.761 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-19 21:14:09.814 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-19 21:14:09.904 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-19 21:14:09.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.911 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.912 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.912 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.912 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.913 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.914 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.914 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.921 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.921 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.923 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.923 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.923 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.924 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:09.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.924 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.925 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.926 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.926 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.926 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.926 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.926 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:09.926 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:09.926 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:09.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.930 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.933 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.933 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.933 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.933 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.933 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.943 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.944 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.944 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.944 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.944 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.944 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.950 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.950 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.950 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.950 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.950 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.952 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.952 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.954 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:09.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.954 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.956 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.956 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.956 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.956 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:09.956 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:09.956 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:09.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.960 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.968 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.971 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.972 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:09.972 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:09.972 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:09.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:09.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:09.972 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:09.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:09.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:09.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:09.974 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:09.974 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:09.982 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-19 21:14:10.519 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-19 21:14:10.519 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-19 21:14:10.522 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.522 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.522 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.522 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.522 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.524 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.525 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.525 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.525 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.525 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.525 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.525 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.525 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.525 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.525 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.526 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.526 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.526 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:10.526 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.526 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.526 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.526 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.526 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.527 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.528 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.528 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.528 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.528 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.528 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:10.528 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:10.528 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:10.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-19 21:14:10.531 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-19 21:14:10.531 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-19 21:14:10.531 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-19 21:14:10.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-19 21:14:10.533 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-19 21:14:10.753 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-19 21:14:10.753 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-19 21:14:10.755 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-19 21:14:10.755 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-19 21:14:10.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.757 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.760 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.760 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.760 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.760 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-19 21:14:10.764 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-19 21:14:10.764 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-19 21:14:10.767 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.767 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.767 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.767 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.767 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.768 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.768 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.768 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.768 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.770 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.770 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.772 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.772 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.774 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.774 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.774 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:10.774 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.774 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.774 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.774 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.774 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.775 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.775 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.775 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.775 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.776 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.776 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:10.776 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:10.776 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:10.778 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.778 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.778 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.778 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.778 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.780 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.780 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.780 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.782 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.782 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.782 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:10.782 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.782 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.782 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.782 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.782 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.784 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.785 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.785 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.786 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.786 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.787 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.787 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.787 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.787 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.788 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:10.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.788 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.790 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.790 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.790 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.790 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.790 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.790 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:10.790 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:10.790 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 21:14:10.793 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.795 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.795 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.795 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.795 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.796 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.796 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.796 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.798 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 21:14:10.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 21:14:10.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 21:14:10.798 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 21:14:10.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 21:14:10.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 21:14:10.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 21:14:10.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 21:14:10.801 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 21:14:10.801 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 21:14:10.801 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 21:14:10.801 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 21:14:10.801 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 21:14:10.801 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:24.558 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-19 22:14:24.559 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-19 22:14:24.559 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-19 22:14:24.559 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-19 22:14:24.559 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-19 22:14:24.559 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-19 22:14:24.559 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-19 22:14:24.559 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-19 22:14:24.559 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-19 22:14:24.570 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-19 22:14:24.571 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-19 22:14:24.572 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-19 22:14:24.572 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-19 22:14:24.572 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-19 22:14:24.572 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-19 22:14:24.572 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-19 22:14:24.572 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-19 22:14:24.576 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.576 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.577 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-19 22:14:24.577 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-19 22:14:24.583 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 22:14:24.583 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 22:14:24.584 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-19 22:14:24.584 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-19 22:14:24.728 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.728 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-19 22:14:24.728 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.728 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.765 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-19 22:14:24.772 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-19 22:14:24.772 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-19 22:14:24.772 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.782 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.798 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.798 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.798 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.799 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:24.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.799 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.800 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.800 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.802 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.802 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.802 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.805 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.806 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.806 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.806 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.808 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:24.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.808 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.808 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.809 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.809 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.809 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.810 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.811 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.811 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.811 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.811 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.811 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.812 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.812 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.812 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:24.812 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.812 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.812 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.812 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.812 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.813 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.814 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.814 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.814 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.814 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.814 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:24.814 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:24.814 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:24.819 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-19 22:14:24.847 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-19 22:14:24.896 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-19 22:14:24.900 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.900 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.900 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.900 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.900 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.901 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.901 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.901 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.901 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.901 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.902 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.903 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.903 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.904 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.904 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.909 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.910 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.910 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.912 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:24.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.912 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.913 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.913 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.913 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.913 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:24.913 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:24.913 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:24.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.915 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.915 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.915 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.915 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.916 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.916 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.916 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.916 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.922 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.922 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.922 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.922 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.922 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.924 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.924 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.924 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.924 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.927 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.927 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.928 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.928 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.928 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.929 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:24.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.929 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.929 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.930 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.930 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.930 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.930 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.930 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:24.930 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:24.930 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:24.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.931 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.931 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.932 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.933 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.933 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.936 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.936 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.937 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:24.937 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:24.937 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:24.939 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:24.939 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:24.939 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:24.943 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-19 22:14:25.371 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-19 22:14:25.371 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-19 22:14:25.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.374 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.375 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.375 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.376 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:25.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.376 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.376 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.377 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.377 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.377 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.377 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:25.377 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:25.377 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:25.379 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-19 22:14:25.380 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-19 22:14:25.380 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-19 22:14:25.380 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-19 22:14:25.380 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-19 22:14:25.381 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-19 22:14:25.547 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-19 22:14:25.547 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-19 22:14:25.548 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-19 22:14:25.548 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.551 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.551 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.551 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.552 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-19 22:14:25.557 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-19 22:14:25.557 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-19 22:14:25.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.561 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.562 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.562 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.564 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.564 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.564 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.564 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.564 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.565 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.565 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.565 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.566 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.566 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.566 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:25.566 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.566 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.566 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.566 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.566 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.567 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.567 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.567 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.567 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.567 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.567 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:25.567 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:25.567 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:25.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.571 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.573 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.573 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.573 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.574 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.574 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.574 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:25.574 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.574 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.574 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.574 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.574 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.575 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.575 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.575 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.577 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.577 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.577 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.578 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.578 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.578 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:25.578 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.578 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.578 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.578 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.578 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.579 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.579 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.579 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.579 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.579 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.579 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:25.579 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:25.579 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:14:25.583 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.583 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.583 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.583 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.583 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.584 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.585 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.585 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.585 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.585 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.585 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.585 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.585 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.585 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.585 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.586 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.586 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.586 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:14:25.586 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:14:25.586 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:14:25.586 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:14:25.586 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:14:25.586 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:14:25.587 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:14:25.587 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:14:25.587 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:14:25.587 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:14:25.587 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:14:25.587 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:14:25.587 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:14:25.587 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:32:57.906 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-19 22:32:57.906 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-19 22:32:57.907 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-19 22:32:57.907 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-19 22:32:57.907 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-19 22:32:57.907 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-19 22:32:57.907 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-19 22:32:57.907 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-19 22:32:57.907 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-19 22:32:57.959 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-19 22:32:57.962 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-19 22:32:57.963 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-19 22:32:57.963 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-19 22:32:57.963 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-19 22:32:57.963 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-19 22:32:57.963 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-19 22:32:57.963 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-19 22:32:57.976 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:57.976 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:57.977 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-19 22:32:57.977 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-19 22:32:57.997 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 22:32:57.997 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-19 22:32:57.998 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-19 22:32:58.090 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-19 22:32:58.493 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.493 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-19 22:32:58.495 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.495 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.763 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-19 22:32:58.774 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-19 22:32:58.775 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-19 22:32:58.775 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.790 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.820 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:58.820 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.820 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.822 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:32:58.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.822 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.822 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.825 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.825 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.825 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:58.825 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:58.825 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:58.836 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:32:58.836 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:32:58.836 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:32:58.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.837 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.837 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.838 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.838 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:58.838 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:58.838 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:58.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.839 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.839 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.840 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:32:58.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.840 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.840 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.842 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.842 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.842 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:58.842 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:58.842 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:58.842 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:32:58.842 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:32:58.842 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:32:58.843 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.843 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.843 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.843 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.843 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.844 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.844 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.844 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:58.844 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:58.844 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:58.844 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.844 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.844 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.844 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.845 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.846 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:32:58.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:58.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:58.846 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:58.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:58.846 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:58.848 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:58.848 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:58.848 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:58.848 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:58.848 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:58.848 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:32:58.848 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:32:58.848 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:32:58.860 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-19 22:32:59.011 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-19 22:32:59.188 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-19 22:32:59.208 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.208 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.208 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.208 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.208 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.209 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.210 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.210 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.210 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.210 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.210 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.210 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.210 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.210 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.210 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.211 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.212 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.212 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.212 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.212 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.212 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.212 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.212 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.212 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.212 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.213 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.213 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.225 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.225 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.227 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.227 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.227 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.227 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.228 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.228 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.228 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.228 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.228 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.228 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.228 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.228 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.228 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.229 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.229 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.229 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:32:59.229 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.229 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.229 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.229 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.230 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.232 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.232 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.232 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.232 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.232 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.232 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:32:59.232 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:32:59.232 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:32:59.236 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.236 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.236 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.236 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.236 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.238 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.238 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.238 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.238 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.239 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.262 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.263 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.263 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.263 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.275 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.275 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.275 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.275 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.275 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.276 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.276 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.276 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.276 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.276 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.277 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.277 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.277 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.277 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.277 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.278 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.278 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.279 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:32:59.279 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.279 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.279 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.279 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.279 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.280 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.280 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.280 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.281 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.281 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.281 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:32:59.281 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:32:59.281 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:32:59.283 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.283 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.283 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.283 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.283 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.285 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.285 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.304 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.310 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.310 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.310 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.310 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.317 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.317 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.317 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.321 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.321 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.321 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:32:59.331 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-19 22:32:59.935 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-19 22:32:59.935 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-19 22:32:59.940 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.941 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.941 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.941 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.941 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.942 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.942 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.942 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.943 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:32:59.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:32:59.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:32:59.943 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:32:59.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:32:59.943 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:32:59.944 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:32:59.944 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:32:59.944 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:32:59.944 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:32:59.944 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:32:59.944 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:32:59.944 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:32:59.944 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:32:59.948 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-19 22:32:59.949 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-19 22:32:59.949 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-19 22:32:59.949 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-19 22:32:59.949 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-19 22:32:59.950 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-19 22:33:00.170 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-19 22:33:00.170 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-19 22:33:00.170 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-19 22:33:00.170 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-19 22:33:00.170 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-19 22:33:00.170 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-19 22:33:00.171 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-19 22:33:00.173 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-19 22:33:00.173 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-19 22:33:00.176 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.176 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.176 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.176 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.176 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.177 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.178 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.178 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.178 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.178 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.178 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-19 22:33:00.186 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-19 22:33:00.186 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-19 22:33:00.188 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.188 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.188 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.188 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.188 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.190 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.190 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.190 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.191 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.192 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.192 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.192 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.192 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.193 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.194 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.194 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.194 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.196 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.196 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.196 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:33:00.197 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.197 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.197 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.197 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.197 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.199 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.199 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.199 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.199 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.199 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.199 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:33:00.199 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:33:00.199 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:33:00.201 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.202 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.202 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.202 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.202 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.203 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.203 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.203 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.203 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.203 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.204 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.204 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.204 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.204 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.204 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.205 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.205 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.205 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:33:00.205 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.205 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.205 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.205 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.205 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.206 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.207 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.207 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.207 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.209 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.209 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.209 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.210 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.211 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.211 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:33:00.211 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.211 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.211 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.211 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.211 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.212 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.212 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.212 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.212 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.212 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.213 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:33:00.213 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:33:00.213 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-19 22:33:00.218 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.218 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.218 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.218 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.218 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.220 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.220 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.220 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.221 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.221 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.221 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-19 22:33:00.222 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-19 22:33:00.222 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-19 22:33:00.222 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-19 22:33:00.222 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-19 22:33:00.222 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-19 22:33:00.223 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-19 22:33:00.223 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-19 22:33:00.223 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-19 22:33:00.223 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-19 22:33:00.223 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-19 22:33:00.223 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-19 22:33:00.223 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-19 22:33:00.223 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
