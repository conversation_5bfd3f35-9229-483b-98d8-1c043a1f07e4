package com.ict.ycwl.pathcalculate.service.Impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.RateLimiter;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pathcalculate.AOP.ConfigFileMothodDataSource;
import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import com.ict.ycwl.pathcalculate.form.AddRouteFrom;
import com.ict.ycwl.pathcalculate.mapper.*;
import com.ict.ycwl.pathcalculate.pojo.*;
import com.ict.ycwl.pathcalculate.pojo.dynamiEntity.TeamF;
import com.ict.ycwl.pathcalculate.service.RouteDetailService;
import com.ict.ycwl.pathcalculate.service.RouteService;
import com.ict.ycwl.pathcalculate.utils.MySQLConnection;
import com.ict.ycwl.pathcalculate.utils.pathOptimization.TSPData;
import com.ict.ycwl.pathcalculate.vo.GroupDataVO;
import com.ict.ycwl.pathcalculate.vo.RouteDataVO;
import com.ict.ycwl.pathcalculate.vo.RouteVO;
import com.ict.ycwl.pathcalculate.vo.TransitDepotRouteVO;
import com.ict.ycwl.pathcalculate.vo.details.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 路径计算service实现类
 */
@Slf4j
@Service
public class RouteServiceImpl extends ServiceImpl<RouteMapper, Route> implements RouteService {

    @Value("${route.version-limit:3}")
    private Integer versionLimit;

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private AccumulationMapper accumulationMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private CarMapper carMapper;

    @Autowired
    private TransitDepotMapper transitDepotMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private RouteDetailService routeDetailService;

    @Autowired
    private RouteDetailMapper routeDetailMapper;

    @Autowired
    private SystemParameterMapper systemParameterMapper;

    @Autowired
    private StoreTimeMapper storeTimeMapper;


    private final RateLimiter limiter = RateLimiter.create(3.0);


    /**
     * 获取路线详情，包含大区、路线、聚集区
     *
     * @return vo
     */
    @Override
    @ConfigFileMothodDataSource(configKey = "jjking.dbPath")
    public List<GroupDataVO> getRouteDetails() {
        List<TeamF> groups = groupMapper.MyselectList(new QueryWrapper<>());
        List<GroupDataVO> result = new ArrayList<>();
        for (int i = 0; i < groups.size(); i++) {
            TeamF group = groups.get(i);
            if ("物流部".equals(group.getTeamName())) {
                groups.remove(group);
            } else {
                GroupDataVO build = GroupDataVO.builder().groupId(group.getTeamId())
                        .routeCount(0)
                        .carCount(0)
                        .groupName(group.getTeamName())
                        .list(new ArrayList<>()).build();
                result.add(build);
            }
        }

        //1.获取中转站列表
        String[] dateList = {"星期一", "星期二", "星期三", "星期四", "星期五"};
        List<TransitDepot> transitDepotList = transitDepotMapper.selectList(null);
        //2.将中转站集合转换成中转站VO集合并找到路线集合、聚集区集合
        for (TransitDepot transitDepot : transitDepotList) {
            List<RouteDateVO> list = new ArrayList<>();

            for (String date : dateList) {
                //1.找到路线集合
                QueryWrapper<Route> routeQueryWrapper = new QueryWrapper<>();
                routeQueryWrapper.eq("transit_depot_id", transitDepot.getTransitDepotId());
                routeQueryWrapper.eq("is_delete", 0);
                routeQueryWrapper.like("route_name", date);
                List<Route> routeList = routeMapper.selectList(routeQueryWrapper);
                //2.将路线集合转换成路线VO集合
                List<RouteDetailsVO> routeDetailsVOS = new ArrayList<>();
                for (Route route : routeList) {
                    // 创建路线VO类并赋值
                    RouteDetailsVO routeDetailsVO = RouteDetailsVO.builder().routeId(route.getRouteId())
                            .routeName(route.getRouteName()).build();
                    //4.添加进路线VOList
                    routeDetailsVOS.add(routeDetailsVO);
                }
                list.add(RouteDateVO.builder().date(date).routeList(routeDetailsVOS).build());
            }

            //3.创建大区VO类并赋值
            TransitDepotDetailsVO transitDepotDetailsVO = TransitDepotDetailsVO.builder().transitDepotId(transitDepot.getTransitDepotId())
                    .transitDepotName(transitDepot.getTransitDepotName())
                    .routeList(list).build();
            //4.添加进路线VOList
            for (GroupDataVO groupVO : result) {
                if (transitDepot.getGroupId().equals(groupVO.getGroupId())) {

                    QueryWrapper<Car> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("transit_depot_id", transitDepot.getTransitDepotId()).eq("status", "1").eq("is_delete", 0).eq("is_fact", 0);

                    long carCount = carMapper.selectCount(queryWrapper) + groupVO.getCarCount();
                    groupVO.setCarCount((int) carCount);

                    int routeSize = 0;
                    for (RouteDateVO routeDateVO : list) {
                        routeSize += routeDateVO.getRouteList().size();
                    }

                    int routeCount = groupVO.getRouteCount() + routeSize;
                    groupVO.setRouteCount(routeCount);

                    groupVO.getList().add(transitDepotDetailsVO);
                }
            }
        }

        return result;
    }


    /**
     * 获取聚集区
     *
     * @return vo
     */
    @Override
    public List<AccumulationDetailsVO> getAccumulationDetails(Long routeId) {
        String polyline = routeMapper.selectById(routeId).getPolyline();
        List<Map<String, Double>> list = getPolylineMap(polyline);
        List<Accumulation> accumulationList = new ArrayList<>();
        for (Map<String, Double> map : list) {
            Double longitude = map.get("longitude");
            Double latitude = map.get("latitude");
            System.out.println(longitude + "," + latitude);
            QueryWrapper<Accumulation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("longitude", longitude);
            queryWrapper.eq("latitude", latitude);
            Accumulation accumulation = null;
            try {
                accumulation = accumulationMapper.selectList(queryWrapper).get(0);
            } catch (Exception e) {
                continue;
            }
            if (accumulation != null) {
                accumulationList.add(accumulation);
            }
        }
        //2.将聚集区列表转换成聚集区VO列表
        List<AccumulationDetailsVO> accumulationDetailsVOS = new ArrayList<>();
        for (Accumulation accumulation : accumulationList) {
            AccumulationDetailsVO accumulationDetailsVO = new AccumulationDetailsVO();
            BeanUtils.copyProperties(accumulation, accumulationDetailsVO);
            accumulationDetailsVOS.add(accumulationDetailsVO);
        }

        return accumulationDetailsVOS;
    }

    /**
     * 获取聚集区下商户信息
     *
     * @return vo
     */
    @Override
    public List<StoreDetailsVO> getStoreDetails(Long accumulationId) {
        //1.根据accumulationId查询店铺信息
        QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
        storeQueryWrapper.eq("accumulation_id", accumulationId);
        storeQueryWrapper.eq("is_delete", 0);
        List<Store> storeList = storeMapper.selectList(storeQueryWrapper);
        //2.转换成VO集合并返回
        List<StoreDetailsVO> storeDetailsVOS = new ArrayList<>();
        for (Store store : storeList) {
            StoreDetailsVO storeDetailsVO = new StoreDetailsVO();
            BeanUtils.copyProperties(store, storeDetailsVO);
            storeDetailsVOS.add(storeDetailsVO);
        }
        return storeDetailsVOS;
    }


    /**
     * 获取地图数据（外层）
     *
     * @return vo
     */
    @Override
    public List<RouteVO> getMapData() {
        QueryWrapper<Route> routeQueryWrapper = new QueryWrapper<>();
        routeQueryWrapper.eq("is_delete", 0);
        List<Route> routeList = routeMapper.selectList(routeQueryWrapper);
        List<RouteVO> routeVOList = new ArrayList<>();
        for (Route route : routeList) {
            List<Map<String, Double>> polylineMap = getPolylineMap(route.getPolyline());
            List<Map<String, Double>> convexMap = getPolylineMap(route.getConvex());
            RouteVO routeVO = new RouteVO();
            BeanUtils.copyProperties(route, routeVO);
            routeVO.setPolyline(polylineMap);
            routeVO.setConvex(convexMap);
            routeVOList.add(routeVO);
        }
        return routeVOList;
    }

    /**
     * 获取大区历史路径列表数据
     *
     * @return vo
     */
    @Override
    public List<TransitDepotRouteVO> getTransitDepotRouteData() {
        List<TransitDepot> transitDepots = transitDepotMapper.selectList(null);
        List<TransitDepotRouteVO> transitDepotRouteVOList = new ArrayList<>();
        for (TransitDepot transitDepot : transitDepots) {
            QueryWrapper<Car> carQueryWrapper = new QueryWrapper<>();
            carQueryWrapper.eq("transit_depot_id", transitDepot.getTransitDepotId());
            List<Car> cars = carMapper.selectList(carQueryWrapper);

            TransitDepotRouteVO transitDepotRouteVO = TransitDepotRouteVO.builder().transitDepotName(transitDepot.getTransitDepotName())
                    .licensePlateNumberList
                            (cars.stream().map(Car::getLicensePlateNumber).collect(Collectors.toList()))
                    .transitDepotId(transitDepot.getTransitDepotId())
                    .build();
            transitDepotRouteVOList.add(transitDepotRouteVO);
        }
        return transitDepotRouteVOList;


    }

    /**
     * 获取路径详细数据
     *
     * @param transitDepotId 中转站ID
     * @param routeName      路线名称
     * @return vo
     */
    @Override
    public RouteDataVO getRouteData(Long transitDepotId, String routeName, String apiKey) throws ApiKeyException {
        QueryWrapper<Route> routeQueryWrapper = new QueryWrapper<>();
        routeQueryWrapper.eq("transit_depot_id", transitDepotId);
        routeQueryWrapper.eq("route_name", routeName);
        Route route = routeMapper.selectOne(routeQueryWrapper);
        if (route == null) {
            return null;
        }
        RouteDataVO routeDataVO = getRouteDataVO(route, apiKey);


        return routeDataVO;

    }

    /**
     * 添加路线数据（替换）
     *
     * @param from 请求参数
     */
    @Override
    @Transactional
    public String addRoute(List<AddRouteFrom> from) {
        // 获取所有中转站ID
        List<Long> transitDepotIdList = from.stream().map(AddRouteFrom::getTransitDepotId).distinct().collect(Collectors.toList());

        // 0. 获取路线版本号
        Map<Long, Integer> versionNum = new HashMap<>();

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期并输出
        String todayDate = currentDate.format(formatter);

        // 获取当前日期保存路径次数
        for (Long transitDepotId : transitDepotIdList) {
            int versionExitNum = getRouteVersionNum(todayDate, transitDepotId);

            // 查看是否超过限制次数
            if (versionExitNum < versionLimit) {
                versionNum.put(transitDepotId, ++versionExitNum);
            } else {
                return "今日保存次数已达上限";
            }
        }

        //1.假删除旧路径
        //1.1.通过大区ID找到旧路径
        QueryWrapper<Route> routeQueryWrapper = new QueryWrapper<>();
        // 通过中转站ID值进行匹配
        routeQueryWrapper.in("transit_depot_id", transitDepotIdList);
        Route routeUpdate = Route.builder().isDelete(true).build();
        //1.2.假删除
        routeMapper.update(routeUpdate, routeQueryWrapper);


        //2.保存新路径
        for (AddRouteFrom addRouteFrom : from) {
            //2.1.将坐标字符串Map转换成字符串
            String polylineString = getPolylineString(addRouteFrom.getPolyline());
            String convexString = getPolylineString(addRouteFrom.getConvex());
            //2.2.转换路径名称
            Integer versionSaveNumber = versionNum.get(addRouteFrom.getTransitDepotId());
            //String routeName = addRouteFrom.getRouteName() + String.format("-%d.0", versionSaveNumber);
            String routeName = addRouteFrom.getRouteName();
            //2.3.转换为实体
            Route route = Route.builder().polyline(polylineString)
                    .convex(convexString)
                    .routeName(routeName)
                    .cargoWeight(addRouteFrom.getCargoWeight())
                    .distance(addRouteFrom.getDistance())
                    .areaId(addRouteFrom.getAreaId())
                    .transitDepotId(addRouteFrom.getTransitDepotId())
                    .createTime(new Timestamp(System.currentTimeMillis()))
                    .updateTime(new Timestamp(System.currentTimeMillis()))
                    .versionNumber(versionSaveNumber)
                    .isDelete(false).workTime(addRouteFrom.getWorkTime())
                    .build();
            //2.4.保存
            routeMapper.insert(route);

            RouteDetail detailBuilder = RouteDetail.builder().routeId(route.getRouteId()).loadingTime(addRouteFrom.getLoadingTime())
                    .transitTime(addRouteFrom.getTransitTime()).deliveryTime(addRouteFrom.getDeliveryTime())
                    .totalTime(addRouteFrom.getWorkTime()).freeewatDist(addRouteFrom.getFreeewatDist()).urabanRoadsDist(addRouteFrom.getUrabanRoadsDist())
                    .townshipRoadsDist(addRouteFrom.getTownshipRoadsDist()).secondTransitTime(addRouteFrom.getSecondTransitTime()).build();

            routeDetailMapper.insert(detailBuilder);

            //2.5 更新聚集区和商铺对应的路线Id
            setAccumulationAndStoreRouteId(route.getRouteId());
        }

        return "保存成功";
    }

    /**
     * 设置聚集区和商铺对应的路线Id
     *
     * @param routeId
     */
    public void setAccumulationAndStoreRouteId(Long routeId) {
        Route route = routeMapper.selectById(routeId);
        List<Map<String, Double>> polylineMap = getPolylineMap(route.getPolyline());
        // 通过poly遍历查找每个Accumulation  -> Accumulation数组
        List<Accumulation> accumulationList = new ArrayList<>();
        for (Map<String, Double> map : polylineMap) {
            // 设置queryWrapper
            Double longitude = map.get("longitude");
            Double latitude = map.get("latitude");
            QueryWrapper<Accumulation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("longitude", longitude);
            queryWrapper.eq("latitude", latitude);
            queryWrapper.eq("is_delete", 0);

            Accumulation accumulation = null;
            try {
                accumulation = accumulationMapper.selectList(queryWrapper).get(0);
            } catch (Exception e) {
                continue;
            }
            if (accumulation != null) {
                accumulationList.add(accumulation);
            }
        }
        // 遍历 Accumulation数组
        for (Accumulation accumulation : accumulationList) {
            // 更新Accumulation的routeId
            Accumulation updateAccumulation = Accumulation.builder()
                    .accumulationId(accumulation.getAccumulationId()).routeId(routeId).build();
            accumulationMapper.updateById(updateAccumulation);

            // 设置Accumulation下所有Store的RouteId
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("accumulation_id", accumulation.getAccumulationId());
            List<Store> storeList = storeMapper.selectList(queryWrapper);
            for (Store store : storeList) {
                Store updateStore = Store.builder().storeId(store.getStoreId())
                        .routeId(routeId).build();
                storeMapper.updateById(updateStore);
            }
        }

    }

    /**
     * 转换 路线坐标点串-获取字符串格式
     *
     * @param mapList
     * @return String
     */
    private static String getPolylineString(List<Map<String, Double>> mapList) {
        List<String> polylineList = new ArrayList<>();
        int size = mapList.size();
        StringBuilder s = new StringBuilder();
        for (Map<String, Double> map : mapList) {
            String polylineAlone = map.get("longitude") + "," + map.get("latitude");
            if (size != 1) {
                polylineList.add(polylineAlone + ";");
            } else {
                polylineList.add(polylineAlone);
            }

            size--;
        }

        for (String string : polylineList) {
            s.append(string);
        }
        return s.toString();
    }

    /**
     * 转换 路线坐标点串-获取Map格式
     *
     * @param
     * @return
     */
    public List<Map<String, Double>> getPolylineMap(String str) {
        //转换polyline数据
        List<Map<String, Double>> list = new ArrayList<>();

        //切割；
        String[] splitOne = str.split(";");
        //切割，
        for (String s : splitOne) {
            String[] splitTwo = s.split(",");
            Map<String, Double> map = new HashMap<>();
            map.put("latitude", Double.parseDouble(splitTwo[1]));
            map.put("longitude", Double.parseDouble(splitTwo[0]));
            list.add(map);
        }
        return list;
    }

    /**
     * 转换 路线坐标点串-获取Map格式
     *
     * @param
     * @return
     */
    private static List<Map<String, Double>> getPolylineMapTmp(String str) {
        //转换polyline数据
        List<Map<String, Double>> list = new ArrayList<>();

        //切割；
        String[] splitOne = str.split(";");
        //切割，
        for (int i = 1; i < splitOne.length; i++) {
            if (i == splitOne.length - 1) {
                break;
            }
            String[] splitTwo = splitOne[i].split(",");
            Map<String, Double> map = new HashMap<>();
            map.put("latitude", Double.parseDouble(splitTwo[1]));
            map.put("longitude", Double.parseDouble(splitTwo[0]));
            list.add(map);
        }
        return list;
    }

    /**
     * 获取路线版本号
     *
     * @param date
     * @param transitDepotId
     * @return
     */
    @Override
    public List<String> getRouteVersion(String date, Long transitDepotId) {
        int routeVersionNum = getRouteVersionNum(date, transitDepotId);

        // 处理数据
        List<String> versionlist = new ArrayList<>();
        for (int i = 1; i <= routeVersionNum; i++) {
            versionlist.add(i + ".0");
        }

        return versionlist;
    }

    /**
     * 获取路线版本号数
     *
     * @param date
     * @param transitDepotId
     * @return
     */
    private int getRouteVersionNum(String date, Long transitDepotId) {
        QueryWrapper<Route> versionWrapper = new QueryWrapper<>();
        versionWrapper.like("create_time", date);
        versionWrapper.eq("transit_depot_id", transitDepotId);
        List<Route> versionList = routeMapper.selectList(versionWrapper);
        List<Integer> versionExitNum = versionList.stream().map(Route::getVersionNumber).distinct().collect(Collectors.toList());
        return versionExitNum.size();
    }

    @Override
    public WorkTimeVo getWorkTime(Long routeId, String polylineStr, String apiKey) throws ApiKeyException {
        // T2
        // 根据routeId获取路线poly
        List<Map<String, Double>> polylineMap = getPolylineMap(polylineStr);

        //对路线坐标进行去重，

        // 通过poly遍历查找每个store  -> Store数组
        List<Store> storeList = new ArrayList<>();
        for (int i = 0; i < polylineMap.size(); i++) {
            Map<String, Double> map = polylineMap.get(i);
            Double longitude = map.get("longitude");
            Double latitude = map.get("latitude");
            System.out.println(longitude + "," + latitude);
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("longitude", longitude);
            queryWrapper.eq("latitude", latitude);
            queryWrapper.eq("is_delete", 0);
            Store store = null;
            try {
                store = storeMapper.selectList(queryWrapper).get(0);
            } catch (Exception e) {
                continue;
            }
            if (store != null) {
                storeList.add(store);
            }
        }

        //获取二次中转时长
        Store store1 = storeList.get(0);
        Long accumulationId1 = store1.getAccumulationId();
        Accumulation accumulation = accumulationMapper.selectById(accumulationId1);

        String erciTime = "0";

        if (accumulation != null) {
            Long transitDepotId = accumulation.getTransitDepotId();
            int count = systemParameterMapper.mySelectCount(transitDepotId);
            if (count != 0) {
                double time = systemParameterMapper.selectSecondByTranId(transitDepotId);
                erciTime = String.valueOf(time);
            }
        }


        // 遍历store数组 算出两个store数组之间的距离并除以 相应的速度  接着累加
        BigDecimal totalDistance = new BigDecimal("0");
        List<Double> distanceList = new ArrayList<>();
        BigDecimal t2 = new BigDecimal("0");

        //计算起点到第一个商铺时间
        Map<String, Double> origin = polylineMap.get(0);
        Store firstStore = storeList.get(0);

        SystemParameter sp = systemParameterMapper.selectById(1);
        //城区时速
        int chengquKMH = (int) sp.getUrbanRoads();
        int xiangzhenKMH = (int) sp.getTownshipRoads();
        int gaosuKMH = (int) sp.getFreeway();
        //默认时速是乡镇
        int tmpSpeed = xiangzhenKMH;//km/h
        //int tempSpeed=40
        if ("0".equals(firstStore.getLocationType())) {//城区
            //tmpSpeed = 30;
            tmpSpeed = chengquKMH;
        }
        DoublePoint pointFirst1 = new DoublePoint(new double[]{origin.get("longitude"), origin.get("latitude")});
        DoublePoint pointFirst2 = new DoublePoint(new double[]{firstStore.getLongitude(), firstStore.getLatitude()});
        //计算中转站到第一个商铺的距离 ，单位米
        double[] distanceFirst = saveDistanceInformation(pointFirst1, pointFirst2, apiKey);
        if (distanceFirst[1] != 0) {
            tmpSpeed = gaosuKMH;
        }
        // 距离除以速度 并t2累加
        BigDecimal speedDecimalFirst = new BigDecimal(tmpSpeed);
        //将距离转换成km
        BigDecimal distanceDecimalFirst = new BigDecimal(distanceFirst[0]).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
        //中转站到第一个商铺的所需时长
        BigDecimal divideFirst = distanceDecimalFirst.divide(speedDecimalFirst, 2, RoundingMode.HALF_UP);//小时
        BigDecimal time = divideFirst.multiply(new BigDecimal("60"));//time单位分钟
        distanceList.add(distanceFirst[0]);
        totalDistance = totalDistance.add(new BigDecimal(distanceFirst[0]));

        t2 = t2.add(time);


        double freeewatDist = 0;
        double urabanRoadsDist = 0;
        double townshipRoadsDist = 0;
        //计算商铺和商铺之间的距离,和时长
        for (int i = 0; i < storeList.size(); i++) {
            if (i == storeList.size() - 1) {
                break;
            }

            Store store = storeList.get(i);
            Store nextStore = storeList.get(i + 1);

            // 查看两个商铺的类型得   速度
            int speed;
            if (store.getLocationType().equals(nextStore.getLocationType())) {
                if (storeList.get(i).getLocationType().equals("0")) {
                    // 城区 -> 城区
                    speed = chengquKMH;
                } else {
                    // 乡镇 -> 乡镇
                    speed = xiangzhenKMH;
                }
            } else {
                // 城区 -> 乡镇 或 乡镇 -> 城区
                speed = (chengquKMH + xiangzhenKMH) / 2;
            }

            // 查看俩个商铺的距离
            DoublePoint point1 = new DoublePoint(new double[]{store.getLongitude(), store.getLatitude()});
            DoublePoint point2 = new DoublePoint(new double[]{nextStore.getLongitude(), nextStore.getLatitude()});
            double[] distance = saveDistanceInformation(point1, point2, apiKey);
            distanceList.add(distance[0]);
            totalDistance = totalDistance.add(new BigDecimal(distance[0]));
            // 距离除以速度 并t2累加
            if (distanceFirst[1] != 0) {
                speed = gaosuKMH;
            }

            if (speed == gaosuKMH) {
                freeewatDist += distance[0];
            } else if (speed == chengquKMH || speed == ((chengquKMH + xiangzhenKMH) / 2)) {
                urabanRoadsDist += distance[0];
            } else if (speed == xiangzhenKMH) {
                townshipRoadsDist += distance[0];
            }

            BigDecimal speedDecimal = new BigDecimal(speed);
            BigDecimal distanceDecimal = new BigDecimal(distance[0]).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
            BigDecimal divide = distanceDecimal.divide(speedDecimal, 2, RoundingMode.HALF_UP);
            BigDecimal multiplyHour = divide.multiply(new BigDecimal("60"));
            t2 = t2.add(multiplyHour);
        }

        //计算终点到最后一个商铺时间
        Map<String, Double> end = polylineMap.get(polylineMap.size() - 1);
        Store endStore = storeList.get(storeList.size() - 1);
        tmpSpeed = xiangzhenKMH;
        if ("0".equals(firstStore.getLocationType())) {
            tmpSpeed = chengquKMH;
        }
        DoublePoint pointEnd1 = new DoublePoint(new double[]{end.get("longitude"), end.get("latitude")});
        DoublePoint pointEnd2 = new DoublePoint(new double[]{endStore.getLongitude(), endStore.getLatitude()});
        double[] distanceEnd = saveDistanceInformation(pointEnd1, pointEnd2, apiKey);
        // 距离除以速度 并t2累加
        if (distanceFirst[1] != 0) {
            tmpSpeed = gaosuKMH;
        }
        BigDecimal speedDecimalEnd = new BigDecimal(tmpSpeed);
        BigDecimal distanceDecimalEnd = new BigDecimal(distanceEnd[0]).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
        BigDecimal divideEnd = distanceDecimalEnd.divide(speedDecimalEnd, 2, RoundingMode.HALF_UP);
        time = divideEnd.multiply(new BigDecimal("60"));
        distanceList.add(distanceEnd[0]);
        totalDistance = totalDistance.add(new BigDecimal(distanceEnd[0]));
        t2 = t2.add(time);

        // T3
        // 统计store集合中accumulationId 计算城区和乡镇的商铺总和
        int cityNum = 0;//城市商铺数量
        int countryNum = 0;//乡村商铺数量

        //最多商铺的密集度系数
        double max = 0;

        List<Long> accumulationIdList = storeList.stream()
                .map(Store::getAccumulationId)
                .distinct()
                .collect(Collectors.toList());
        for (Long accumulationId : accumulationIdList) {
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("accumulation_id", accumulationId).eq("is_delete", 0);
            List<Store> list = storeMapper.selectList(queryWrapper);
            //统计最大密集度系数
            if (list.size() > max) {
                max = list.size();
            }
        }
        //商铺平均卸货时长
        double cityTime = sp.getShoreUnloadCityTime();//城市
        double townshipTime = sp.getShoreUnloadTownshipTime();//乡镇

        BigDecimal a = new BigDecimal("0");
        BigDecimal b = new BigDecimal("0");
        for (Long accumulationId : accumulationIdList) {
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("accumulation_id", accumulationId).eq("is_delete", 0);
            List<Store> list = storeMapper.selectList(queryWrapper);

            for (Store store : list) {
                if ("0".equals(store.getLocationType())) {
                    cityNum++;
                } else {
                    countryNum++;
                }
            }
            //计算当前打卡点的密集度系数
            double curr = list.size() / (max * sp.getAccumulationIntensity());
            //计算当前路线的卸货时长
            BigDecimal cityTime1 = new BigDecimal(String.valueOf(cityNum * (cityTime / curr)));
            a = a.add(cityTime1);
            String s = String.valueOf(countryNum * (townshipTime / curr));
            b = b.add(new BigDecimal(s));


            //记录卸货时长
            StoreTime storeTime = new StoreTime();
            double time1 = cityTime1.add(new BigDecimal(s)).doubleValue();
            storeTime.setTime(time1);
            Accumulation accumulation1 = accumulationMapper.selectById(accumulationId);
            storeTime.setLongitude(String.valueOf(accumulation1.getLongitude()));
            storeTime.setLatitude(String.valueOf(accumulation1.getLatitude()));
            //将当前聚集区下所有的商铺
            StoreTime storeTime1 = storeTimeMapper.selectOne(new LambdaQueryWrapper<StoreTime>().eq(StoreTime::getLongitude, storeTime.getLongitude()).eq(StoreTime::getLatitude, storeTime.getLatitude()).last("limit 1"));
            if (storeTime1 != null) {
                storeTime1.setTime(time1);
                storeTimeMapper.updateById(storeTime1);
            } else {
                storeTimeMapper.insert(storeTime);
            }
        }
        //设置装车时长
        String loadingTime = String.valueOf(sp.getLoadingTime());
        System.out.println("坐标点串实际数：" + polylineMap.size());
        System.out.println("实际跟据点串获得聚集区/打卡点数（不含起点终点）：" + storeList.size());
        System.out.println("路线总距离：" + totalDistance);
        System.out.println("路线数据集个数：" + distanceList.size());
        System.out.println("T1(装车时长)：" + loadingTime);
        System.out.println("T2（途中时长）：" + t2);
        System.out.println("城区商铺总数：" + cityNum);
        System.out.println("乡镇商铺总数：" + countryNum);


        //BigDecimal a = new BigDecimal(cityNum).multiply(new BigDecimal("2.34"));
        //BigDecimal a = new BigDecimal(cityNum).multiply(new BigDecimal(String.valueOf(cityTime)));
        //后续查询数据库进行填充
        //乡村成功平均卸货时长
        //BigDecimal b = new BigDecimal(countryNum).multiply(new BigDecimal("2.9"));
        //BigDecimal b = new BigDecimal(countryNum).multiply(new BigDecimal(String.valueOf(townshipTime)));
        System.out.println("城区商铺时长：" + a);
        System.out.println("乡镇商铺时长：" + b);

        BigDecimal t3 = a.add(b);
        System.out.println("T3（卸货配送时长）：" + t3);


        //加上二次中转时长
        BigDecimal add = t3.add(new BigDecimal(erciTime));


        // T1+T2+T3


        BigDecimal t1 = new BigDecimal(loadingTime);
        BigDecimal t = t1.add(t2).add(add);
        System.out.println("T（总时长）：" + t);

        System.out.println(distanceList);

        // 更新路线工作时长
        if (routeId != null) {
            Route route = routeMapper.selectById(routeId);
            if (!route.getIsDelete()) {
                RouteDetail routeDetail = RouteDetail.builder().routeId(routeId)
                        .accumulationCount(storeList.size())
                        .countryCount(countryNum)
                        .cityCount(cityNum)
                        .loadingTime(t1.toString())
                        .transitTime(t2.toString())
                        .deliveryTime(t3.toString())
                        .totalTime(t.toString())
                        .secondTransitTime(Double.parseDouble(erciTime))
                        .freeewatDist(freeewatDist)
                        .urabanRoadsDist(urabanRoadsDist)
                        .townshipRoadsDist(townshipRoadsDist)
                        .build();
                //查询路线是否存在，如果存在就更新、
                RouteDetail routeDetail1 = routeDetailMapper.selectByRouteId(routeDetail.getRouteId());
                if (routeDetail1 == null) {
                    routeDetailService.save(routeDetail);
                } else {
                    routeDetailMapper.updateById(routeDetail);
                }
                Route update = Route.builder().routeId(routeId).workTime(t.toString()).build();
                this.updateById(update);
            }
        }
        WorkTimeVo workTimeVo = new WorkTimeVo();
        workTimeVo.setDeliveryTime(t3.toString());
        workTimeVo.setLoadingTime(t1.toString());
        workTimeVo.setTransitTime(t2.toString());
        workTimeVo.setWorkTime(t.toString());

        workTimeVo.setSecondTransitTime(Double.parseDouble(erciTime));
        workTimeVo.setFreeewatDist(freeewatDist);
        workTimeVo.setUrabanRoadsDist(urabanRoadsDist);
        workTimeVo.setTownshipRoadsDist(townshipRoadsDist);


        return workTimeVo;
    }


    @Override
    public WorkTimeVo getWorkTime(Long routeId, String polylineStr, String apiKey, Long transitId) throws ApiKeyException {
        //对路线坐标进行去重，
        String[] points = polylineStr.split(";");
        TransitDepot transitDepot = transitDepotMapper.selectOne(new LambdaQueryWrapper<TransitDepot>().select(TransitDepot::getLongitude, TransitDepot::getLatitude).eq(TransitDepot::getTransitDepotId, transitId).last("limit 1"));
        String tansitIds = transitDepot.getLongitude() + "," + transitDepot.getLatitude();
        ArrayList<String> listaa = new ArrayList<>();
        listaa.add(tansitIds);
        for (String point : points) {
            if (tansitIds.equals(point)) {
                continue;
            }
            if (!listaa.contains(point)) {
                listaa.add(point);
            }
        }
        listaa.add(tansitIds);
        String join = String.join(";", listaa);
        polylineStr = join;

        // T2
        // 根据routeId获取路线poly
        List<Map<String, Double>> polylineMap = getPolylineMap(polylineStr);


        // 通过poly遍历查找每个store  -> Store数组
        List<Store> storeList = new ArrayList<>();
        for (int i = 0; i < polylineMap.size(); i++) {
            Map<String, Double> map = polylineMap.get(i);
            Double longitude = map.get("longitude");
            Double latitude = map.get("latitude");
            System.out.println(longitude + "," + latitude);
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("longitude", longitude);
            queryWrapper.eq("latitude", latitude);
            queryWrapper.eq("is_delete", 0);
            Store store = null;
            try {
                store = storeMapper.selectList(queryWrapper).get(0);
            } catch (Exception e) {
                continue;
            }
            if (store != null) {
                storeList.add(store);
            }
        }

        //获取二次中转时长
        Store store1 = storeList.get(0);
        Long accumulationId1 = store1.getAccumulationId();
        Accumulation accumulation = accumulationMapper.selectById(accumulationId1);

        String erciTime = "0";

        if (accumulation != null) {
            Long transitDepotId = accumulation.getTransitDepotId();
            int count = systemParameterMapper.mySelectCount(transitDepotId);
            if (count != 0) {
                double time = systemParameterMapper.selectSecondByTranId(transitDepotId);
                erciTime = String.valueOf(time);
            }
        }


        // 遍历store数组 算出两个store数组之间的距离并除以 相应的速度  接着累加
        BigDecimal totalDistance = new BigDecimal("0");
        List<Double> distanceList = new ArrayList<>();
        BigDecimal t2 = new BigDecimal("0");

        //计算起点到第一个商铺时间
        Map<String, Double> origin = polylineMap.get(0);
        Store firstStore = storeList.get(0);

        SystemParameter sp = systemParameterMapper.selectById(1);
        //城区时速
        int chengquKMH = (int) sp.getUrbanRoads();
        int xiangzhenKMH = (int) sp.getTownshipRoads();
        int gaosuKMH = (int) sp.getFreeway();
        //默认时速是乡镇
        int tmpSpeed = xiangzhenKMH;//km/h
        //int tempSpeed=40
        if ("0".equals(firstStore.getLocationType())) {//城区
            //tmpSpeed = 30;
            tmpSpeed = chengquKMH;
        }
        DoublePoint pointFirst1 = new DoublePoint(new double[]{origin.get("longitude"), origin.get("latitude")});
        DoublePoint pointFirst2 = new DoublePoint(new double[]{firstStore.getLongitude(), firstStore.getLatitude()});
        //计算中转站到第一个商铺的距离 ，单位米
        double[] distanceFirst = saveDistanceInformation(pointFirst1, pointFirst2, apiKey);
        if (distanceFirst[1] != 0) {
            tmpSpeed = gaosuKMH;
        }
        // 距离除以速度 并t2累加
        BigDecimal speedDecimalFirst = new BigDecimal(tmpSpeed);
        //将距离转换成km
        BigDecimal distanceDecimalFirst = new BigDecimal(distanceFirst[0]).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
        //中转站到第一个商铺的所需时长
        BigDecimal divideFirst = distanceDecimalFirst.divide(speedDecimalFirst, 2, RoundingMode.HALF_UP);//小时
        BigDecimal time = divideFirst.multiply(new BigDecimal("60"));//time单位分钟
        distanceList.add(distanceFirst[0]);
        totalDistance = totalDistance.add(new BigDecimal(distanceFirst[0]));

        t2 = t2.add(time);


        double freeewatDist = 0;
        double urabanRoadsDist = 0;
        double townshipRoadsDist = 0;
        //计算商铺和商铺之间的距离,和时长
        for (int i = 0; i < storeList.size(); i++) {
            if (i == storeList.size() - 1) {
                break;
            }

            Store store = storeList.get(i);
            Store nextStore = storeList.get(i + 1);

            // 查看两个商铺的类型得   速度
            int speed;
            if (store.getLocationType().equals(nextStore.getLocationType())) {
                if (storeList.get(i).getLocationType().equals("0")) {
                    // 城区 -> 城区
                    speed = chengquKMH;
                } else {
                    // 乡镇 -> 乡镇
                    speed = xiangzhenKMH;
                }
            } else {
                // 城区 -> 乡镇 或 乡镇 -> 城区
                speed = (chengquKMH + xiangzhenKMH) / 2;
            }

            // 查看俩个商铺的距离
            DoublePoint point1 = new DoublePoint(new double[]{store.getLongitude(), store.getLatitude()});
            DoublePoint point2 = new DoublePoint(new double[]{nextStore.getLongitude(), nextStore.getLatitude()});
            double[] distance = saveDistanceInformation(point1, point2, apiKey);
            distanceList.add(distance[0]);
            totalDistance = totalDistance.add(new BigDecimal(distance[0]));
            // 距离除以速度 并t2累加
            if (distanceFirst[1] != 0) {
                speed = gaosuKMH;
            }

            if (speed == gaosuKMH) {
                freeewatDist += distance[0];
            } else if (speed == chengquKMH || speed == ((chengquKMH + xiangzhenKMH) / 2)) {
                urabanRoadsDist += distance[0];
            } else if (speed == xiangzhenKMH) {
                townshipRoadsDist += distance[0];
            }

            BigDecimal speedDecimal = new BigDecimal(speed);
            BigDecimal distanceDecimal = new BigDecimal(distance[0]).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
            BigDecimal divide = distanceDecimal.divide(speedDecimal, 2, RoundingMode.HALF_UP);
            BigDecimal multiplyHour = divide.multiply(new BigDecimal("60"));
            t2 = t2.add(multiplyHour);
        }

        //计算终点到最后一个商铺时间
        Map<String, Double> end = polylineMap.get(polylineMap.size() - 1);
        Store endStore = storeList.get(storeList.size() - 1);
        tmpSpeed = xiangzhenKMH;
        if ("0".equals(firstStore.getLocationType())) {
            tmpSpeed = chengquKMH;
        }
        DoublePoint pointEnd1 = new DoublePoint(new double[]{end.get("longitude"), end.get("latitude")});
        DoublePoint pointEnd2 = new DoublePoint(new double[]{endStore.getLongitude(), endStore.getLatitude()});
        double[] distanceEnd = saveDistanceInformation(pointEnd1, pointEnd2, apiKey);
        // 距离除以速度 并t2累加
        if (distanceFirst[1] != 0) {
            tmpSpeed = gaosuKMH;
        }
        BigDecimal speedDecimalEnd = new BigDecimal(tmpSpeed);
        BigDecimal distanceDecimalEnd = new BigDecimal(distanceEnd[0]).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
        BigDecimal divideEnd = distanceDecimalEnd.divide(speedDecimalEnd, 2, RoundingMode.HALF_UP);
        time = divideEnd.multiply(new BigDecimal("60"));
        distanceList.add(distanceEnd[0]);
        totalDistance = totalDistance.add(new BigDecimal(distanceEnd[0]));
        t2 = t2.add(time);

        // T3
        // 统计store集合中accumulationId 计算城区和乡镇的商铺总和
        int cityNum = 0;//城市商铺数量
        int countryNum = 0;//乡村商铺数量

        //最多商铺的密集度系数
        double max = 0;

        List<Long> accumulationIdList = storeList.stream()
                .map(Store::getAccumulationId)
                .distinct()
                .collect(Collectors.toList());
        for (Long accumulationId : accumulationIdList) {
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("accumulation_id", accumulationId).eq("is_delete", 0);
            List<Store> list = storeMapper.selectList(queryWrapper);
            //统计最大密集度系数
            if (list.size() > max) {
                max = list.size();
            }
        }
        //商铺平均卸货时长
        double cityTime = sp.getShoreUnloadCityTime();//城市
        double townshipTime = sp.getShoreUnloadTownshipTime();//乡镇

        BigDecimal a = new BigDecimal("0");
        BigDecimal b = new BigDecimal("0");
        for (Long accumulationId : accumulationIdList) {
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("accumulation_id", accumulationId).eq("is_delete", 0);
            List<Store> list = storeMapper.selectList(queryWrapper);

            for (Store store : list) {
                if ("0".equals(store.getLocationType())) {
                    cityNum++;
                } else {
                    countryNum++;
                }
            }
            //计算当前打卡点的密集度系数
            double curr = list.size() / (max * sp.getAccumulationIntensity());
            //计算当前路线的卸货时长
            BigDecimal cityTime1 = new BigDecimal(String.valueOf(cityNum * (cityTime / curr)));
            a = a.add(cityTime1);
            String s = String.valueOf(countryNum * (townshipTime / curr));
            b = b.add(new BigDecimal(s));


            //记录卸货时长
            StoreTime storeTime = new StoreTime();
            double time1 = cityTime1.add(new BigDecimal(s)).doubleValue();
            storeTime.setTime(time1);
            Accumulation accumulation1 = accumulationMapper.selectById(accumulationId);
            storeTime.setLongitude(String.valueOf(accumulation1.getLongitude()));
            storeTime.setLatitude(String.valueOf(accumulation1.getLatitude()));
            //将当前聚集区下所有的商铺
            StoreTime storeTime1 = storeTimeMapper.selectOne(new LambdaQueryWrapper<StoreTime>().eq(StoreTime::getLongitude, storeTime.getLongitude()).eq(StoreTime::getLatitude, storeTime.getLatitude()).last("limit 1"));
            if (storeTime1 != null) {
                storeTime1.setTime(time1);
                storeTimeMapper.updateById(storeTime1);
            } else {
                storeTimeMapper.insert(storeTime);
            }
        }
        //设置装车时长
        String loadingTime = String.valueOf(sp.getLoadingTime());
        System.out.println("坐标点串实际数：" + polylineMap.size());
        System.out.println("实际跟据点串获得聚集区/打卡点数（不含起点终点）：" + storeList.size());
        System.out.println("路线总距离：" + totalDistance);
        System.out.println("路线数据集个数：" + distanceList.size());
        System.out.println("T1(装车时长)：" + loadingTime);
        System.out.println("T2（途中时长）：" + t2);
        System.out.println("城区商铺总数：" + cityNum);
        System.out.println("乡镇商铺总数：" + countryNum);


        //BigDecimal a = new BigDecimal(cityNum).multiply(new BigDecimal("2.34"));
        //BigDecimal a = new BigDecimal(cityNum).multiply(new BigDecimal(String.valueOf(cityTime)));
        //后续查询数据库进行填充
        //乡村成功平均卸货时长
        //BigDecimal b = new BigDecimal(countryNum).multiply(new BigDecimal("2.9"));
        //BigDecimal b = new BigDecimal(countryNum).multiply(new BigDecimal(String.valueOf(townshipTime)));
        System.out.println("城区商铺时长：" + a);
        System.out.println("乡镇商铺时长：" + b);

        BigDecimal t3 = a.add(b);
        System.out.println("T3（卸货配送时长）：" + t3);


        //加上二次中转时长
        BigDecimal add = t3.add(new BigDecimal(erciTime));


        // T1+T2+T3


        BigDecimal t1 = new BigDecimal(loadingTime);
        BigDecimal t = t1.add(t2).add(add);
        System.out.println("T（总时长）：" + t);

        System.out.println(distanceList);

        // 更新路线工作时长
        if (routeId != null) {
            Route route = routeMapper.selectById(routeId);
            if (!route.getIsDelete()) {
                RouteDetail routeDetail = RouteDetail.builder().routeId(routeId)
                        .accumulationCount(storeList.size())
                        .countryCount(countryNum)
                        .cityCount(cityNum)
                        .loadingTime(t1.toString())
                        .transitTime(t2.toString())
                        .deliveryTime(t3.toString())
                        .totalTime(t.toString())
                        .secondTransitTime(Double.parseDouble(erciTime))
                        .freeewatDist(freeewatDist)
                        .urabanRoadsDist(urabanRoadsDist)
                        .townshipRoadsDist(townshipRoadsDist)
                        .build();
                //查询路线是否存在，如果存在就更新、
                RouteDetail routeDetail1 = routeDetailMapper.selectByRouteId(routeDetail.getRouteId());
                if (routeDetail1 == null) {
                    routeDetailService.save(routeDetail);
                } else {
                    routeDetailMapper.updateById(routeDetail);
                }
                Route update = Route.builder().routeId(routeId).workTime(t.toString()).build();
                this.updateById(update);
            }
        }
        WorkTimeVo workTimeVo = new WorkTimeVo();
        workTimeVo.setDeliveryTime(t3.toString());
        workTimeVo.setLoadingTime(t1.toString());
        workTimeVo.setTransitTime(t2.toString());
        workTimeVo.setWorkTime(t.toString());

        workTimeVo.setSecondTransitTime(Double.parseDouble(erciTime));
        workTimeVo.setFreeewatDist(freeewatDist);
        workTimeVo.setUrabanRoadsDist(urabanRoadsDist);
        workTimeVo.setTownshipRoadsDist(townshipRoadsDist);


        return workTimeVo;
    }


    int num = 0;

    public static List<Dist> query(String origin, String destination) {
        ArrayList<Dist> dists = new ArrayList<>();
        String sql = "select * from dist where origin=? and destination=?";
        try (Connection connection = MySQLConnection.getConnection(); PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, origin);
            preparedStatement.setString(2, destination);
            ResultSet resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                double dist = resultSet.getDouble("dist");
                Dist obj = new Dist();
                obj.setDist(dist);
                obj.setOrigin(origin);
                obj.setDestination(destination);
                dists.add(obj);
            }
        } catch (SQLException e) {
            System.out.println("插入数据失败！" + e.getMessage());
        }
        return dists;
    }

    public static void save(Dist dist) {
        String sql = "INSERT INTO dist (id, origin,destination,dist) VALUES (?,?,?,?)";
        try (Connection connection = MySQLConnection.getConnection();
             PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, null);
            preparedStatement.setString(2, dist.getOrigin());
            preparedStatement.setString(3, dist.getDestination());
            preparedStatement.setDouble(4, dist.getDist());
            preparedStatement.executeUpdate();
        } catch (SQLException e) {
            System.out.println("插入数据失败！" + e.getMessage());
        }
    }


    public double[] saveDistanceInformation(DoublePoint point1, DoublePoint point2, String apiKey) throws ApiKeyException {
        String url = "https://restapi.amap.com/v3/direction/driving";
        //        String apiKey = "3acb45c690a8aed5095eff50887689f6";
        // 拼接经度和纬度字符串
        String origin = point1.getPoint()[0] + "," + point1.getPoint()[1];
        String destination = point2.getPoint()[0] + "," + point2.getPoint()[1];

        // 构建请求URL
        String requestUrl = url + "?origin=" + origin + "&destination=" + destination + "&number=FD08088&extensions=all&output=json&key=" + apiKey;

        double dist = 0.0;
        double tolls = 0;
        JsonNode rootNode = null;
        for (int i = 0; i < 3; i++) {
            try {
                // 发送HTTP请求
                URL urlObj = new URL(requestUrl);
                HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
                connection.setRequestMethod("GET");

                // 读取响应数据
                Scanner scanner = new Scanner(connection.getInputStream());
                StringBuilder response = new StringBuilder();
                while (scanner.hasNextLine()) {
                    response.append(scanner.nextLine());
                }
                scanner.close();
                ObjectMapper objectMapper = new ObjectMapper();
                rootNode = objectMapper.readTree(response.toString());
                System.out.println(rootNode);
                //            double dist = rootNode.path("route").path("paths").get(0).path("distance").asDouble();
                JsonNode pathsNode = rootNode.path("route").path("paths");
                dist = pathsNode.get(0).path("distance").asDouble();
                tolls = pathsNode.get(0).path("tolls").asDouble();
                break;
            } catch (Exception e) {
                System.out.println("api异常s:" + e.getMessage());
            }
        }

        return new double[]{dist, tolls};//单位m
    }

    @Override
    public RouteDataVO getRouteDataVO(Route route, String apiKey) throws ApiKeyException {
        RouteDataVO routeDataVO = new RouteDataVO();
        BeanUtils.copyProperties(route, routeDataVO);
        //转换路线坐标点串
        List<Map<String, Double>> map = getPolylineMap(route.getPolyline());
        routeDataVO.setPolyline(map);
        if (route.getConvex() != null) {
            map = getPolylineMap(route.getConvex());
        }
        routeDataVO.setConvex(map);
        // 获取工作时长
        String workTime = route.getWorkTime();
        if (StrUtil.isEmpty(workTime)) {
            workTime = getWorkTime(route.getRouteId(), route.getPolyline(), apiKey).toString();
        }
        routeDataVO.setWorkTime(workTime);
        return routeDataVO;
    }

    /*
     * 单个班组的路径优化-目的减少工作时长,会自动优化工作时长超过8个小时或者超过平均工作时长的班组
     * */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, timeout = 300)
    public String optimizingOneGroupRoute(String apikey) {
        //查询每个班组下的中转站
        List<String> groupTransits = transitDepotMapper.selectGroupToTransit();
        //一次处理每一个班组
        //统计班组工作时长 key：班组id value：班组工作时长
        ArrayList<AveTimeWorkTime> aveTimeWorkTimes = new ArrayList<>();
        double aveGroupWorkTime = 0;
        for (String groupTransit : groupTransits) {
            AveTimeWorkTime workTime = new AveTimeWorkTime();
            //分割中转站id
            String[] split = groupTransit.split(",");
            //该班组总的工作时长
            double workTimeSum = 0;
            //该班组总路线数
            int routeSum = 0;
            long groupId = 0;
            for (String id : split) {
                TransitDepot transitDepot = transitDepotMapper.selectById(id);
                groupId = transitDepot.getGroupId();
                List<Route> routes = routeMapper.selectList(new QueryWrapper<Route>().eq("is_delete", 0).eq("transit_depot_id", Long.parseLong(id)));
                //统计每一个路线的工作时长
                routeSum += routes.size();
                for (Route route : routes) {
                    if(route==null||route.getWorkTime()==null||StringUtil.isBlank(route.getWorkTime())){
                        continue;
                    }
                    workTimeSum += Double.parseDouble(route.getWorkTime());
                }
            }
            workTimeSum = workTimeSum / 60 / routeSum;
            workTime.setTeamId(groupId);
            workTime.setRouteNum(routeSum);
            workTime.setTimeHour(workTimeSum);
            aveGroupWorkTime += workTimeSum;
            aveTimeWorkTimes.add(workTime);
        }
        aveGroupWorkTime = aveGroupWorkTime / aveTimeWorkTimes.size();
        //遍历班组，路径优化那些班组工作时长超过8h或者超过平均工作时长的班组
        //如果班组工作时长低于平均工作时长1h，则停止优化
        for (AveTimeWorkTime entry : aveTimeWorkTimes) {
            Long teamId = entry.getTeamId();
            Double workCurrTime = entry.getTimeHour();
            //超过平均工作时长+1个小时
            if (workCurrTime > 8 || workCurrTime > aveGroupWorkTime + 1) {
                List<TransitDepot> transitDepots = transitDepotMapper.selectList(new QueryWrapper<TransitDepot>().eq("is_delete", 0).eq("group_id", teamId));
                for (TransitDepot transitDepot : transitDepots) {
                    //如果当前班组的平均工作时长小于等于平均工作时长就跳过
                    if (entry.getTimeHour() <= aveGroupWorkTime) {
                        continue;
                    }
                    //⬇️⬇️⬇️⬇️⬇️⬇️只对超出8个小时的班组进行工作时长优化
                    List<Route> isDelete = routeMapper.selectList(new QueryWrapper<Route>().eq("is_delete", 0).eq("transit_depot_id", transitDepot.getTransitDepotId()));
                    TransitDepot transitDepot2 = transitDepotMapper.selectById(transitDepot.getTransitDepotId());
                    String longitude = transitDepot2.getLongitude();
                    String latitude = transitDepot2.getLatitude();
                    double[] transitDepotDouble = {Double.parseDouble(longitude), Double.parseDouble(latitude)};
                    String transitDepotPoint = longitude + "," + latitude;
                    //路径去重(V3有用)(只保留一个中转站)
                    ArrayList<Route> routes = new ArrayList<>();
                    for (Route route : isDelete) {
                        ArrayList<String> list = new ArrayList<>();
                        String polyline = route.getPolyline();
                        String[] split = polyline.split(";");
                        for (int i1 = 0; i1 < split.length; i1++) {
                            if (!list.contains(split[i1])) {
                                list.add(split[i1]);
                            }
                        }
                        //判断去重集合中是否有中转站
                        if (!list.contains(transitDepotPoint)) {
                            //如果没有，则往路线中添加中转站
                            list.add(transitDepotPoint);
                        }
                        String join = String.join(";", list);
                        route.setPolyline(join);
                        routes.add(route);
                    }

                    //采用遗传算法运行时长说明⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️
                    //注：每个中转站都能优化，优化效果较好，但是计算时间较长，时长主要花费在调用高德api计算两点之间的距离。
                    //如果一条路线中有40个坐标，那么大概要调用api，40X40/2次，800次，免费的api，最多一秒3次，避免超出1s请求上限，每获取3次手动暂停1s。
                    // 加上获取数据后，处理时间，意味着获取3条数据要2s，800/3；266X2，大概9分钟，这是一条路线
                    //每个中转站中大概有25-30条路线，每条路线平均下来有20个坐标，大概每条路线大概处理2分钟，25条路线，处理50分钟。
                    //性能说明⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️除去调用api的时间一个中转站大概计算40s，优化算法就没有说明意义了，如何优化坐标矩阵
                    //路径优化(启发式-遗传算法）⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️
                    //参数：double[][] [0][0]：坐标经度 [0][1]：坐标纬度
                    List<String> polyline = routes.stream().map(Route::getPolyline).collect(Collectors.toList());
                    //遍历每一条路线
                    for (int i = 0; i < polyline.size(); i++) {
                        String[] split = polyline.get(i).split(";");
                        //去重不要起点
                        int len = split.length;
                        double[][] cityPosition = new double[len][2];
                        for (int j = 0; j < len; j++) {
                            String[] split1 = split[j].split(",");
                            cityPosition[j][0] = Double.parseDouble(split1[0]);
                            cityPosition[j][1] = Double.parseDouble(split1[1]);
                        }
                        String[] run = TSPData.run(cityPosition);
                        //收集当条路线的优化结果
                        ArrayList<String> res = new ArrayList<>();
                        for (String s : run) {
                            int i1 = Integer.parseInt(s);
                            res.add(split[i1 - 1]);
                        }
                        res.add(split[Integer.parseInt(run[0]) - 1]);
                        String path = String.join(";", res);

                        //获取原始路线的工作时长
                        Route route = routes.get(i);
                        double times = Double.parseDouble(route.getWorkTime());//单位分钟
                        //计算优化后路线的工作时长
                        BigDecimal bigDecimal = null;
                        try {
                            //bigDecimal = myGetWorkTime(null, path, "a123fae9da370c45984c58720bf3ac7c", transitDepotDouble);
                            bigDecimal = myGetWorkTime(null, path, apikey, transitDepotDouble);
                        } catch (ApiKeyException e) {
                            throw new RuntimeException(e);
                        }
                        //计算优化后该条路径的工作时长
                        double timed = bigDecimal.doubleValue();
                        //如果优化后的工作时长低于优化前的工作时长就替换
                        if (timed < times) {
                            //替代优化前该条路线的工作时长，
                            routes.get(i).setPolyline(path);
                            //如果，整体班组工作时长低于平均工作时长的0.75h就停止优化该班组
                            double h = (times - timed) / 60 / entry.getRouteNum();
                            entry.setTimeHour(entry.getTimeHour() - h);
                            //更新该条路线的工作时长
                            routes.get(i).setWorkTime(String.valueOf(timed));
                            //更新数据库
                            routeMapper.updateById(routes.get(i));
                            //如果当前班组的平均工作时长小于等于平均工作时长就跳过
                            if (entry.getTimeHour() <= aveGroupWorkTime) {
                                break;
                            }
                        }
                    }
                    //处理完一个中转站后就更新班组平均工作时长
                    double timesum = 0;
                    for (AveTimeWorkTime aveTimeWorkTime : aveTimeWorkTimes) {
                        timesum += aveTimeWorkTime.getTimeHour();
                    }
                    aveGroupWorkTime = timesum / aveTimeWorkTimes.size();
                    //启发式-遗传算法⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️
                }
            }
        }
        return "一次班组路径优化完成";
    }

    @Override
    public List<Long> getTranIds() {
        List<Long> tansIds = transitDepotMapper.selectTransitIds();
        return tansIds;
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, timeout = 300)
    //@Async
    public String asyncAvgTime(Long aLong, String apikey) throws ApiKeyException {
        SystemParameter sp = systemParameterMapper.selectById(1);
        List<TransitDepot> transitDepots = transitDepotMapper.selectList(new LambdaQueryWrapper<TransitDepot>().eq(TransitDepot::getIsDelete, 0).eq(TransitDepot::getTransitDepotId, aLong));
        //创建routeId-points-Map
        for (TransitDepot transitDepot : transitDepots) {
            String traPoint = transitDepot.getLongitude() + "," + transitDepot.getLatitude();
            //查询数据库中当前中转站下的路线
            List<Route> routes = routeMapper.selectList(new LambdaQueryWrapper<Route>().eq(Route::getTransitDepotId, transitDepot.getTransitDepotId()).eq(Route::getIsDelete, 0));
            for (Route route : routes) {
                ArrayList<String> list = new ArrayList<>();
                String polyline = route.getPolyline();
                String[] split = polyline.split(";");
                for (String s : split) {
                    if (!traPoint.equals(s) && !list.contains(s)) {
                        list.add(s);
                    }
                }
                list.add(0, traPoint);
                list.add(traPoint);
                route.setPolyline(String.join(";", list));
            }

            HashMap<Long, List<String>> map = new HashMap<>();
            double avgTime = 0;
            //统计每条路线下打卡点最多的商铺数量
            for (Route route : routes) {
                //初始化map
                Long routeId = route.getRouteId();
                String[] split = route.getPolyline().split(";");
                ArrayList<String> list = new ArrayList<>();
                int max = 0;
                //去重中转站坐标
                for (String s : split) {
                    //统计每条路线中最多的商铺数
                    int accStoreNum = getAccStoreNum(s.split(",")[0], s.split(",")[1]);
                    if (accStoreNum > max) {
                        max = accStoreNum;
                    }
                    if (!traPoint.equals(s)) {
                        list.add(s);
                    }
                }
                //计算路径中的坐标距离中转站的距离，按从近到远排序坐标，并添加到map集合中
                list.sort(new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        String[] split1 = o1.split(",");
                        String p1 = split1[0] + "," + split1[1];
                        String[] split2 = o2.split(",");
                        String p2 = split2[0] + "," + split2[1];
//                        double v1 = cString(p1, traPoint);
//                        double v2 = cString(p2, traPoint);
                        //方案二，根据打卡点的卸货时长进行排序
                        Double v1 = storeTimeMapper.selectTime(split1[0], split1[1]);
                        if (v1 == null) {
                            double sum=0;
                            //查询城区商铺数量
                            int chengquNum = 0;
                            int xiangzhengNum = 0;
                            List<Accumulation> accumulations = accumulationMapper.selectList(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getLongitude, split1[0]).eq(Accumulation::getLatitude, split1[1]));
                            if (accumulations != null && accumulations.size() > 0) {
                                Accumulation accumulation = accumulations.get(0);
                                Long accumulationId = accumulation.getAccumulationId();
                                List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulationId));
                                for (Store store : stores) {
                                    if ("1".equals(store.getLocationType())) {
                                        xiangzhengNum++;
                                    } else {
                                        chengquNum++;
                                    }
                                }
                            }

                            //查询城镇商铺数量
                            sum += chengquNum * 2.34;
                            sum += xiangzhengNum * 2.9;
                            v1=sum;
                        }
                        Double v2 = storeTimeMapper.selectTime(split2[0], split2[1]);
                        if (v2 == null) {
                            double sum=0;
                                //查询城区商铺数量
                                int chengquNum = 0;
                                int xiangzhengNum = 0;
                                List<Accumulation> accumulations = accumulationMapper.selectList(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getLongitude, split2[0]).eq(Accumulation::getLatitude, split2[1]));
                                if (accumulations != null && accumulations.size() > 0) {
                                    Accumulation accumulation = accumulations.get(0);
                                    Long accumulationId = accumulation.getAccumulationId();
                                    List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulationId));
                                    for (Store store : stores) {
                                        if ("1".equals(store.getLocationType())) {
                                            xiangzhengNum++;
                                        } else {
                                            chengquNum++;
                                        }
                                    }
                                }
                                //查询城镇商铺数量
                            sum += chengquNum * 2.34;
                            sum += xiangzhengNum * 2.9;
                            v2=sum;
                        }
                        //根据
                        return Double.compare(v1, v2);
                    }
                });
                map.put(routeId, list);
                //计算平均工作时长
                avgTime += Double.parseDouble(route.getWorkTime());
            }
            //计算平均工作时长
            avgTime = avgTime / routes.size();
            routes.sort(new Comparator<Route>() {
                @Override
                public int compare(Route o1, Route o2) {
                    return Double.compare(Double.parseDouble(o2.getWorkTime()), Double.parseDouble(o1.getWorkTime()));
                }
            });
            int jianju = 35;
            //依次将工作时间长的路线的坐标移动到工作时长短的路径中
            int j = 2;
            while (j-- > 0) {
                int start = 0;
                for (int i = 0; i < routes.size(); i++) {
                    Route route = routes.get(start);
                    ArrayList<String> list0 = new ArrayList<>(Arrays.asList(route.getPolyline().split(";")));
                    Long routeId = route.getRouteId();

                    //当前循环中工作时长最长
                    double longTime = Double.parseDouble(routes.get(start).getWorkTime());
                    //当前循环中工作时长最短
                    Route route1 = routes.get(routes.size() - 1);
                    ArrayList<String> list = new ArrayList<>(Arrays.asList(route1.getPolyline().split(";")));
                    List<String> rout1Map = map.get(route1.getRouteId());


                    String[] split = route.getPolyline().split(";");
                    ArrayList<String> list3 = new ArrayList<>();
                    //去重中转站坐标
                    for (String s : split) {
                        if (!traPoint.equals(s)) {
                            list3.add(s);
                        }
                    }
                    //计算路径中的坐标距离中转站的距离，按从近到远排序坐标，并添加到map集合中
                    list3.sort(new Comparator<String>() {
                        @Override
                        public int compare(String o1, String o2) {
                            String[] split1 = o1.split(",");
                            String p1 = split1[0] + "," + split1[1];
                            String[] split2 = o2.split(",");
                            String p2 = split2[0] + "," + split2[1];
                            double v1 = cString(p1, traPoint);
                            double v2 = cString(p2, traPoint);
                            return Double.compare(v1, v2);
                        }
                    });
                    map.put(routeId, list3);

                    //插入位置
                    int index = list.size() - 2;

                    double shortTime = Double.parseDouble(routes.get(routes.size() - 1).getWorkTime());
                    //如果要移动的路径的工作时间小于，则跳过
                    if (longTime < avgTime + jianju) {
                        break;
                    }
                    //从map
                    System.out.println(list0.size());
                    List<String> points = map.get(routeId);
                    //从坐标中依次去最近的坐标添加到工作时长最短的路线中
                    int yidong = 0;
                    while (longTime > avgTime + jianju) {
                        if(points.size()<=3){
                            break;
                        }
                        //在移动路线中选择增加工作时长最少的坐标添加到最短工作时长的路线中
                        String point = points.get(yidong);
                        //在路线中找到当前坐标所在的位置
                        int idn = 0;
                        for (int i1 = 0; i1 < list0.size(); i1++) {
                            if (list0.get(i1).equals(point)) {
                                idn = i1;
                                break;
                            }
                        }

                        //计算少去坐标减少的工作时长
                        double jianTime = getRmoTime(point, list0, idn, apikey);
                        //计算增加坐标增加的工作时长
                        double addTime = getAddTime(point, list, index, apikey);

                        //更新工作时长
                        longTime -= jianTime;
                        shortTime += addTime;

                        routes.get(start).setWorkTime(String.valueOf(longTime));
                        routes.get(routes.size() - 1).setWorkTime(String.valueOf(shortTime));
                        //更新少去坐标的路线的数据
                        list0.remove(point);
                        routes.get(start).setPolyline(String.join(";", list0));
                        points.remove(point);
                        sort(points, traPoint);
                        map.put(routeId, points);
                        //更新增加坐标的路线的数据
                        list.add(index, point);
                        routes.get(routes.size() - 1).setPolyline(String.join(";", list));
                        rout1Map.add(point);
                        sort(rout1Map, traPoint);
                        map.put(route1.getRouteId(), rout1Map);

                        //将移动后的打卡点的routeid修改为接收打卡点的路径
                        double accLongitude = Double.parseDouble(point.split(",")[0]);
                        double accLatitude = Double.parseDouble(point.split(",")[1]);
                        Long routeId1 = routes.get(routes.size() - 1).getRouteId();
                        int count = accumulationMapper.updateRouteId(accLongitude, accLatitude, routeId1);
                        //将打卡点下的商铺的routeId修改为接收打卡点的路径id
                        int count1 = storeMapper.updateRouteId(accLongitude, accLatitude, routeId1);


                        //重新计算平均工作时长
                        avgTime = 0;
                        for (Route route2 : routes) {
                            avgTime += Double.parseDouble(route2.getWorkTime());
                        }
                        avgTime /= routes.size();
                        if (shortTime > avgTime||shortTime>480) {
                            break;
                        }
                    }
                    //重新排序路线的工作时长
                    routes.sort(new Comparator<Route>() {
                        @Override
                        public int compare(Route o1, Route o2) {
                            return Double.compare(Double.parseDouble(o2.getWorkTime()), Double.parseDouble(o1.getWorkTime()));
                        }
                    });
                }
            }
            //平衡后的工作时长
            for (Route route : routes) {
                String data = "一Time: " + route.getWorkTime();
                System.out.println(data);
            }
            System.out.println("平均时长：" + avgTime);

            /*//遗传算法
            if (Double.parseDouble(routes.get(0).getWorkTime()) > 510) {
                //进行遗传算法
                double a1 = 0;
                for (Route route : routes) {
                    double[][] aDouble = getDouble(route.getPolyline());
                    String[] run = TSPData.run(aDouble);

                    System.out.println("最短路线：");
                    for (String s : run) {
                        System.out.print(s + "->");
                    }
                    System.out.println();
                    System.out.println("1111: " + route.getPolyline());

                    String path = getPath(route.getPolyline(), run);
                    System.out.println("path: " + path);
                    //WorkTimeVo workTime1 = getWorkTime(null, path, "a123fae9da370c45984c58720bf3ac7c");
                    WorkTimeVo workTime1 = getWorkTime(null, path, apikey);
                    route.setWorkTime(workTime1.getWorkTime());
                    route.setPolyline(path);
                    a1 += Double.parseDouble(workTime1.getWorkTime());
                    System.out.println("Runtime: " + workTime1);
                }
                //打印遗传后的工作时长
                for (Route route : routes) {
                    String data = "遗传Time: " + route.getWorkTime();
                    System.out.println(data);
                }
                System.out.println(a1 / routes.size());
            }*/

            /*//平衡
            routes.sort(new Comparator<Route>() {
                @Override
                public int compare(Route o1, Route o2) {
                    return Double.compare(Double.parseDouble(o2.getWorkTime()), Double.parseDouble(o1.getWorkTime()));
                }
            });
            if (Double.parseDouble(routes.get(0).getWorkTime()) > 510) {
                for (int ll = 0; ll < 2; ll++) {

                    int start = 0;
                    for (int i = 0; i < routes.size(); i++) {
                        Route route = routes.get(start);
                        ArrayList<String> list0 = new ArrayList<>(Arrays.asList(route.getPolyline().split(";")));
                        Long routeId = route.getRouteId();

                        //当前循环中工作时长最长
                        double longTime = Double.parseDouble(routes.get(start).getWorkTime());
                        //当前循环中工作时长最短
                        Route route1 = routes.get(routes.size() - 1);
                        ArrayList<String> list = new ArrayList<>(Arrays.asList(route1.getPolyline().split(";")));
                        List<String> rout1Map = map.get(route1.getRouteId());


                        String[] split = route.getPolyline().split(";");
                        ArrayList<String> list3 = new ArrayList<>();
                        //去重中转站坐标
                        for (String s : split) {
                            if (!traPoint.equals(s)) {
                                list3.add(s);
                            }
                        }
                        //计算路径中的坐标距离中转站的距离，按从近到远排序坐标，并添加到map集合中
                        list3.sort(new Comparator<String>() {
                            @Override
                            public int compare(String o1, String o2) {
                                String[] split1 = o1.split(",");
                                String p1 = split1[0] + "," + split1[1];
                                String[] split2 = o2.split(",");
                                String p2 = split2[0] + "," + split2[1];
                                double v1 = cString(p1, traPoint);
                                double v2 = cString(p2, traPoint);
                                return Double.compare(v1, v2);
                            }
                        });
                        map.put(routeId, list3);


                        //插入位置
                        int index = list.size() - 2;

                        double shortTime = Double.parseDouble(routes.get(routes.size() - 1).getWorkTime());
                        //如果要移动的路径的工作时间小于，则跳过
                        if (longTime < avgTime + jianju) {
                            break;
                        }
                        //从map
                        System.out.println(list0.size());
                        List<String> points = map.get(routeId);
                        //从坐标中依次去最近的坐标添加到工作时长最短的路线中
                        int yidong = 0;
                        while (longTime > avgTime + jianju) {
                            //在移动路线中选择增加工作时长最少的坐标添加到最短工作时长的路线中

                            String point = points.get(yidong);
                            //在路线中找到当前坐标所在的位置
                            int idn = 0;
                            for (int i1 = 0; i1 < list0.size(); i1++) {
                                if (list0.get(i1).equals(point)) {
                                    idn = i1;
                                    break;
                                }
                            }
                            //计算少去坐标减少的工作时长
                            double jianTime = getRmoTime(point, list0, idn, apikey);
                            //计算增加坐标增加的工作时长
                            double addTime = getAddTime(point, list, index, apikey);

                            //更新工作时长
                            longTime -= jianTime;
                            shortTime += addTime;

                            routes.get(start).setWorkTime(String.valueOf(longTime));
                            routes.get(routes.size() - 1).setWorkTime(String.valueOf(shortTime));
                            //更新少去坐标的路线的数据
                            list0.remove(point);
                            routes.get(start).setPolyline(String.join(";", list0));
                            points.remove(point);
                            sort(points, traPoint);
                            map.put(routeId, points);
                            //更新增加坐标的路线的数据
                            list.add(index, point);
                            routes.get(routes.size() - 1).setPolyline(String.join(";", list));
                            rout1Map.add(point);
                            sort(rout1Map, traPoint);
                            map.put(route1.getRouteId(), rout1Map);


                            //将移动后的打卡点的routeid修改为接收打卡点的路径
                            double accLongitude = Double.parseDouble(point.split(",")[0]);
                            double accLatitude = Double.parseDouble(point.split(",")[1]);
                            Long routeId1 = routes.get(routes.size() - 1).getRouteId();
                            int count = accumulationMapper.updateRouteId(accLongitude, accLatitude, routeId1);
                            //将打卡点下的商铺的routeId修改为接收打卡点的路径id
                            int count1 = storeMapper.updateRouteId(accLongitude, accLatitude, routeId1);

                            //重新计算平均工作时长
                            avgTime = 0;
                            for (Route route2 : routes) {
                                avgTime += Double.parseDouble(route2.getWorkTime());
                            }
                            avgTime /= routes.size();
                            if (shortTime > avgTime) {
                                break;
                            }
                        }
                        //重新排序路线的工作时长
                        routes.sort(new Comparator<Route>() {
                            @Override
                            public int compare(Route o1, Route o2) {
                                return Double.compare(Double.parseDouble(o2.getWorkTime()), Double.parseDouble(o1.getWorkTime()));
                            }
                        });
                    }
                }

            }

            //遗传算法
            if (Double.parseDouble(routes.get(0).getWorkTime()) > 510) {
                //进行遗传算法
                double a1 = 0;
                for (Route route : routes) {
                    double[][] aDouble = getDouble(route.getPolyline());
                    String[] run = TSPData.run(aDouble);

                    System.out.println("最短路线：");
                    for (String s : run) {
                        System.out.print(s + "->");
                    }
                    System.out.println();
                    System.out.println("1111: " + route.getPolyline());

                    String path = getPath(route.getPolyline(), run);
                    System.out.println("path: " + path);
                    WorkTimeVo workTime1 = getWorkTime(null, path, apikey);
                    route.setWorkTime(workTime1.getWorkTime());
                    route.setPolyline(path);
                    a1 += Double.parseDouble(workTime1.getWorkTime());
                    System.out.println("Runtime: " + workTime1);
                }
                //打印遗传后的工作时长
                for (Route route : routes) {
                    String data = "遗传Time: " + route.getWorkTime();
                    System.out.println(data);
                }
                System.out.println(a1 / routes.size());
            }*/


            //打印遗传后的工作时长
            double aa2 = 0;
            for (Route route : routes) {
                aa2 += Double.parseDouble(route.getWorkTime());
                System.out.println("time: " + route.getWorkTime());
            }
            System.out.println(aa2 / routes.size());

            //将数据修改之后的数据进行更新
            for (Route route : routes) {
                routeMapper.updateById(route);
                List<RouteDetail> routeDetails = routeDetailMapper.selectList(new LambdaQueryWrapper<RouteDetail>().eq(RouteDetail::getRouteId, route.getRouteId()));
                if (routeDetails != null && routeDetails.size() > 0) {
                    RouteDetail routeDetail = routeDetails.get(0);
                    routeDetail.setTotalTime(route.getWorkTime());
                    //计算中途时长
                    String polyline = route.getPolyline();
                    String[] split = polyline.split(";");
                    double chengqudistSum = 0;
                    double xiangzhengdistSuen = 0;
                    double xieTime = 0;
                    for (int i = 0; i < split.length - 1; i++) {
                        String curr = split[i];
                        String next = split[i + 1];
                        //查询每个商铺的类型z
                        String type = "0";
                        List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, Double.parseDouble(curr.split(",")[0])).eq(Store::getLatitude, Double.parseDouble(curr.split(",")[1])));
                        if (stores != null && stores.size() > 0) {
                            Store store = stores.get(0);
                            type = store.getLocationType();
                        }
                        List<Store> stores1 = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, Double.parseDouble(next.split(",")[0])).eq(Store::getLatitude, Double.parseDouble(next.split(",")[1])));
                        if (stores1 != null && stores1.size() > 0) {
                            Store store = stores1.get(0);
                            String locationType = store.getLocationType();
                            if (!type.equals(locationType)) {
                                type = "1";
                            }
                        }

                        List<Dist> dists = distMapper.selectList(new LambdaQueryWrapper<Dist>().eq(Dist::getOrigin, curr).eq(Dist::getDestination, next));
                        if (dists != null && dists.size() != 0) {
                            Dist dist = dists.get(0);
                            if ("0".equals(type)) {
                                chengqudistSum += dist.getDist();
                            } else {
                                xiangzhengdistSuen += dist.getDist();
                            }
                        } else {
                            double v = saveDistanceInformation(curr, next, apikey);
                            if ("0".equals(type)) {
                                chengqudistSum += v;
                            } else {
                                xiangzhengdistSuen += v;
                            }
                        }
                        //计算卸货时长
                        List<StoreTime> storeTimes = storeTimeMapper.selectList(new LambdaQueryWrapper<StoreTime>().eq(StoreTime::getLongitude, curr.split(",")[0]).eq(StoreTime::getLatitude, curr.split(",")[1]));
                        if (storeTimes != null && storeTimes.size() > 0) {
                            StoreTime storeTime = storeTimes.get(0);
                            xieTime += storeTime.getTime();
                        }
                    }
                    //计算中途距离
                    routeDetail.setUrabanRoadsDist(chengqudistSum);
                    routeDetail.setTownshipRoadsDist(xiangzhengdistSuen);
                    routeDetail.setTransitTime(String.valueOf((chengqudistSum / 1000 / sp.getUrbanRoads() * 60) + (xiangzhengdistSuen / 1000 / sp.getTownshipRoads() * 60)));
                    //计算卸货时长
                    routeDetail.setDeliveryTime(String.valueOf(xieTime));
                    routeDetailMapper.updateById(routeDetail);
                }
            }
        }
        //方法结束
        System.out.println("中转站id：" + aLong);
        return "中转站id：" + aLong;
    }


    //根据打卡点坐标统计出当前打卡点的商铺数量
    public int getAccStoreNum(String longitude, String latitude) {
        List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, longitude).eq(Store::getLatitude, latitude));
        if (stores != null && stores.size() > 0) {
            Store store = stores.get(0);
            Long accumulationId = store.getAccumulationId();
            Long aLong1 = storeMapper.selectCount(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulationId));
            return Math.toIntExact(aLong1);
        }
        return 1;
    }

    @Override
    public boolean updateWorkTime(String apikey) throws ApiKeyException {
        try {
            List<Route> routes = routeMapper.selectList(new LambdaQueryWrapper<Route>().eq(Route::getIsDelete, 0).eq(Route::getTransitDepotId, 1));
            for (Route route : routes) {
                Long routeId = route.getRouteId();
                //修改中途时长
                WorkTimeVo workTime = getWorkTime(routeId, route.getPolyline(), apikey);
                System.out.println(workTime);
            }
        } catch (ApiKeyException e) {
            return false;
        }
        return true;
    }

    @Override
    public AjaxResult updateWorkTimeOne(double freeewatDist, double urabanRoadDist, double townshipRoadsDist, double loadingTime) {
        if (freeewatDist == 0 || urabanRoadDist == 0 || townshipRoadsDist == 0) {
            return AjaxResult.error("时速不能为0");
        }
        List<RouteDetail> routeDetails = routeDetailMapper.selectList(null);
        SystemParameter sp = systemParameterMapper.selectById(1);
        double freeway = sp.getFreeway();
        double urbanRoads = sp.getUrbanRoads();
        double townshipRoads = sp.getTownshipRoads();
        for (RouteDetail routeDetail : routeDetails) {
            double transitTime = Double.parseDouble(routeDetail.getTransitTime());
            double workTime = Double.parseDouble(routeDetail.getTotalTime());
            Long routeId = routeDetail.getRouteId();
            //处理高速公路
            if (routeDetail.getFreeewatDist() != 0) {
                routeDetail.setTransitTime(String.valueOf(Double.parseDouble(routeDetail.getTransitTime()) + (routeDetail.getFreeewatDist() / 1000 / freeway - routeDetail.getFreeewatDist() / 1000 / freeewatDist) / 60));
            }
            //处理城区公路
            if (routeDetail.getUrabanRoadsDist() != 0) {
                routeDetail.setTransitTime(String.valueOf(Double.parseDouble(routeDetail.getTransitTime()) + (routeDetail.getUrabanRoadsDist() / 1000 / urbanRoads - routeDetail.getUrabanRoadsDist() / 1000 / urabanRoadDist) / 60));
            }
            //处理乡镇公路
            if (routeDetail.getTownshipRoadsDist() != 0) {
                routeDetail.setTransitTime(String.valueOf(Double.parseDouble(routeDetail.getTransitTime()) + (routeDetail.getTownshipRoadsDist() / 1000 / townshipRoads - routeDetail.getTownshipRoadsDist() / 1000 / townshipRoadsDist) / 60));
            }
            //计算总时长变化
            //中途时长变化
            if (Double.parseDouble(routeDetail.getTransitTime()) > 0) {
                workTime += Double.parseDouble(routeDetail.getTransitTime()) - transitTime;
            }
            //装车时长变化
            workTime += loadingTime - Double.parseDouble(routeDetail.getLoadingTime());
            //二次中转时长变化
            //根据路线id查询出对应的中转站
            Long tranId = routeMapper.selectTranByRoutId(routeId);
            Double v = systemParameterMapper.selectSecondByTranId(tranId);
            if (v == null) {
                v = (double) 0;
            }
            routeDetail.setSecondTransitTime(v);
            routeDetail.setLoadingTime(String.valueOf(loadingTime));
            workTime += v - routeDetail.getSecondTransitTime();
            routeDetail.setTotalTime(String.valueOf(workTime));
            routeMapper.myUpdate(routeId, String.valueOf(workTime));
            routeDetailMapper.updateById(routeDetail);
        }
        return AjaxResult.success("修改成功");
    }

    @Override
    public AjaxResult deleteTranWorkTime() {
        List<RouteDetail> routeDetails = routeDetailMapper.selectList(null);
        for (RouteDetail routeDetail : routeDetails) {
            Long routeId = routeDetail.getRouteId();
            double workTime = Double.parseDouble(routeDetail.getTotalTime());
            //二次中转时长变化
            //根据路线id查询出对应的中转站
            Long tranId = routeMapper.selectTranByRoutId(routeId);
            Double v = systemParameterMapper.selectSecondByTranId(tranId);
            if (v == null) {
                v = (double) 0;
            }
            routeDetail.setSecondTransitTime(v);
            workTime += v - routeDetail.getSecondTransitTime();
            routeDetail.setTotalTime(String.valueOf(workTime));
            routeMapper.myUpdate(routeId, String.valueOf(workTime));
            routeDetailMapper.updateById(routeDetail);
        }
        return AjaxResult.success("修改成功");
    }

    @Override
    public double queryTime(Long routeId) {
        Route route = routeMapper.selectById(routeId);
        String polyline = route.getPolyline();
        String[] points = polyline.split(";");
        double sumTime = 0;
        LinkedHashSet<String> set = new LinkedHashSet<>();
        for (int i = 1; i < points.length - 1; i++) {
            set.add(points[i]);
        }
        for (String point : set) {
            String[] p = point.split(",");
            String longitude = p[0];
            String latitude = p[1];
            StoreTime storeTime = storeTimeMapper.selectOne(new LambdaQueryWrapper<StoreTime>().eq(StoreTime::getLongitude, longitude).eq(StoreTime::getLatitude, latitude).last("limit 1"));
            if (storeTime != null) {
                sumTime += storeTime.getTime();
            }
        }
        return sumTime;
    }

    @Override
    public AjaxResult queryGroupAllId() {
        //查询所有已经启用的中转站
        List<TransitDepot> transitDepots = transitDepotMapper.selectList(new LambdaQueryWrapper<TransitDepot>().eq(TransitDepot::getIsDelete, 0).eq(TransitDepot::getStatus, 1));
        List<Long> collect = transitDepots.stream().map(TransitDepot::getGroupId).distinct().sorted().collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    public void sort(List<String> list, String traPoint) {
        list.sort(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                String[] split1 = o1.split(",");
                String p1 = split1[0] + "," + split1[1];
                String[] split2 = o2.split(",");
                String p2 = split2[0] + "," + split2[1];
                double v1 = cString(p1, traPoint);
                double v2 = cString(p2, traPoint);
                return Double.compare(v1, v2);
            }
        });
    }

    //计算减去工作时长变化
    public double getRmoTime(String point, ArrayList<String> route, int index, String apikey) throws ApiKeyException {
        int qian = index - 1;
        int hou = index + 1;
        if (route.size() == 2) {
            qian = 0;
            hou = 1;
        }
        if (qian == -1) {
            qian = 0;
        }
        if (hou == route.size()) {
            hou--;
        }
        SystemParameter sp = systemParameterMapper.selectById(1);

        //计算当前坐标距离前一个坐标的距离
        double v = saveDistanceInformation(route.get(qian), point, apikey);
        //计算当前坐标距离后一个坐标的距离
        double v1 = saveDistanceInformation(point, route.get(hou), apikey);
        //计算前一个坐标到后一个坐标的距离
        double v2 = saveDistanceInformation(route.get(qian), route.get(hou), apikey);
        //用当前坐标到前后两个坐标的距离之和减去，前一个坐标到后一个坐标的距离
        double sum = (v + v1 - v2) / 1000 / ((sp.getUrbanRoads() + sp.getTownshipRoads()) / 2) * 60;
        //减去周围
        double longitude = Double.parseDouble(point.split(",")[0]);
        double latitude = Double.parseDouble(point.split(",")[1]);

        StoreTime storeTime = storeTimeMapper.selectOne(new LambdaQueryWrapper<StoreTime>().eq(StoreTime::getLongitude, longitude).eq(StoreTime::getLatitude, latitude));
        double time = 0;
        if (storeTime == null) {
            //查询城区商铺数量
            int chengquNum = 0;
            int xiangzhengNum = 0;
            List<Accumulation> accumulations = accumulationMapper.selectList(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getLongitude, longitude).eq(Accumulation::getLatitude, latitude));
            if (accumulations != null && accumulations.size() > 0) {
                Accumulation accumulation = accumulations.get(0);
                Long accumulationId = accumulation.getAccumulationId();
                List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulationId));
                for (Store store : stores) {
                    if ("1".equals(store.getLocationType())) {
                        xiangzhengNum++;
                    } else {
                        chengquNum++;
                    }
                }
            }
            //查询城镇商铺数量
            time += xiangzhengNum * sp.getShoreUnloadTownshipTime();
            time += chengquNum * sp.getShoreUnloadCityTime();
        } else {
            time = storeTime.getTime();
        }
        //返回减少的工作时长
        return sum + time;
    }

    //计算增加工作时长变化
    public double getAddTime(String point, ArrayList<String> route, int index, String apikey) throws ApiKeyException {
        int qian = index - 1;
        int hou = index;
        if (route.size() == 2) {
            qian = 0;
            hou = 1;
        }
        if (qian == -1) {
            qian = 0;
        }
        if (hou == route.size()) {
            hou--;
        }
        //计算插入位置距离前一个坐标的距离
        double v = saveDistanceInformation(route.get(qian), point, apikey);
        //计算插入位置距离后一个坐标的距离
        double v1 = saveDistanceInformation(point, route.get(hou), apikey);
        //计算前一个坐标到后一个坐标的距离
        double v2 = saveDistanceInformation(route.get(qian), route.get(hou), apikey);

        double longitude = Double.parseDouble(point.split(",")[0]);
        double latitude = Double.parseDouble(point.split(",")[1]);

        SystemParameter sp = systemParameterMapper.selectById(1);
        //用当前坐标到前后两个坐标的距离之和减去，前一个坐标到后一个坐标的距离
        double sum = (v + v1 - v2) / 1000 / ((sp.getUrbanRoads() + sp.getTownshipRoads()) / 2) * 60;
        //返回增加的工作时长
        StoreTime storeTime = storeTimeMapper.selectOne(new LambdaQueryWrapper<StoreTime>().eq(StoreTime::getLongitude, longitude).eq(StoreTime::getLatitude, latitude));
        double time = 0;
        if (storeTime == null) {
            //查询城区商铺数量
            int chengquNum = 0;
            int xiangzhengNum = 0;
            List<Accumulation> accumulations = accumulationMapper.selectList(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getLongitude, longitude).eq(Accumulation::getLatitude, latitude));
            if (accumulations != null && accumulations.size() > 0) {
                Accumulation accumulation = accumulations.get(0);
                Long accumulationId = accumulation.getAccumulationId();
                List<Store> stores = storeMapper.selectList(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulationId));
                for (Store store : stores) {
                    if ("1".equals(store.getLocationType())) {
                        xiangzhengNum++;
                    } else {
                        chengquNum++;
                    }
                }
            }
            //查询城镇商铺数量
            time += xiangzhengNum * sp.getShoreUnloadTownshipTime();
            time += chengquNum * sp.getShoreUnloadCityTime();
        } else {
            time = storeTime.getTime();
        }
        return sum + time;
    }


    public double saveDistanceInformation(String doublePoint1, String doublePoint2, String apiKey) throws ApiKeyException {
        //查询数据库，查看是否有该条数据，有则直接返回距离
        DoublePoint point1 = new DoublePoint(new double[]{Double.parseDouble(doublePoint1.split(",")[0]), Double.parseDouble(doublePoint1.split(",")[1])});
        DoublePoint point2 = new DoublePoint(new double[]{Double.parseDouble(doublePoint2.split(",")[0]), Double.parseDouble(doublePoint2.split(",")[1])});
        // 拼接经度和纬度字符串
        String url = "https://restapi.amap.com/v3/direction/driving";
//        String apiKey = "3acb45c690a8aed5095eff50887689f6";
        //String apiKey = "a123fae9da370c45984c58720bf3ac7c";
        // 拼接经度和纬度字符串
        String origin = point1.getPoint()[0] + "," + point1.getPoint()[1];
        String destination = point2.getPoint()[0] + "," + point2.getPoint()[1];
        if (origin.equals(destination)) {
            return 1;
        }
        List<Dist> dist1 = distMapper.selectList(new QueryWrapper<Dist>().eq("origin", origin).eq("destination", destination));
        List<Dist> dist3 = distMapper.selectList(new QueryWrapper<Dist>().eq("origin", destination).eq("destination", origin));
        if (dist1.size() > 0) {
            if (dist1.get(0).getDist() > 0) {
                return dist1.get(0).getDist();
            }
        }
        if (dist3.size() > 0) {
            if (dist3.get(0).getDist() > 0) {
                return dist3.get(0).getDist();
            }
        }
        // 构建请求URL
        String requestUrl = url + "?origin=" + origin + "&destination=" + destination + "&strategy=2" + "&number=FD08088&extensions=all&output=json&key=" + apiKey;

        double dist = 0.0;
        for (int i = 0; i < 3; i++) {
            try {
                URL urlObj = new URL(requestUrl);
                HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
                connection.setRequestMethod("GET");
                // 读取响应数据
                Scanner scanner = new Scanner(connection.getInputStream());
                StringBuilder response = new StringBuilder();
                while (scanner.hasNextLine()) {
                    response.append(scanner.nextLine());
                }
                scanner.close();
                // 解析响应JSON数据
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode rootNode = objectMapper.readTree(response.toString());
                System.out.println(rootNode);
                JsonNode pathsNode = rootNode.path("route").path("paths");
                System.out.println("-----------------------");
                dist = pathsNode.get(0).path("distance").asDouble();
                System.out.println(dist);
                break;
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        QueryWrapper<Dist> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("origin", origin)
                .eq("destination", destination)
                .last("LIMIT 1");  // 性能优化，仅查询1条记录
        Dist existDist = distMapper.selectOne(queryWrapper);
        if (existDist != null) {
            UpdateWrapper<Dist> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("origin", origin)
                    .eq("destination", destination)
                    .set("dist", dist);  // 仅更新dist字段
            distMapper.update(null, updateWrapper);
        } else {
            //插入数据
            Dist newDist = new Dist();
            newDist.setDist(dist);
            newDist.setOrigin(origin);
            newDist.setDestination(destination);
            distMapper.insert(newDist);
        }
        return dist;//单位m
    }

    @Autowired
    private DistMapper distMapper;

    public double cString(String point1, String point2) {
        double lat1 = Double.parseDouble(point1.split(",")[1]);
        double lon1 = Double.parseDouble(point1.split(",")[0]);
        double lat2 = Double.parseDouble(point2.split(",")[1]);
        double lon2 = Double.parseDouble(point2.split(",")[0]);
        //计算两个经纬度之间的距离
        final int EARTH_RADIUS_KM = 6371;
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return Math.round(EARTH_RADIUS_KM * c * 1000.0) / 1000.0;
    }

    //搭配遗传算法使用
    public BigDecimal myGetWorkTime(Long routeId, String polylineStr, String apiKey, double[] transitDepotDouble) throws ApiKeyException {
        // T2
        // 根据routeId获取路线poly
        List<Map<String, Double>> polylineMap = getPolylineMap(polylineStr);
        if (polylineMap.size() == 0) {
            return new BigDecimal("0");
        }
        //路线是打卡点连接而成

        // 通过poly遍历查找每个store  -> Store数组
        List<Store> storeList = new ArrayList<>();
        for (int i = 0; i < polylineMap.size(); i++) {
            Map<String, Double> map = polylineMap.get(i);
            Double longitude = map.get("longitude");
            Double latitude = map.get("latitude");
            //中转站坐标
            double longitude1 = transitDepotDouble[0];
            double latitude1 = transitDepotDouble[1];

            System.out.println(longitude + "," + latitude);
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("longitude", longitude);
            queryWrapper.eq("latitude", latitude);
            Store store = null;
            try {
                store = storeMapper.selectList(queryWrapper).get(0);
            } catch (Exception e) {
                continue;
            }
            //说明当前坐标是一个中转站
            if (longitude == longitude1 && latitude == latitude1) {
                Store store1 = new Store();
                store1.setLocationType("1");
                store1.setLongitude(longitude1);
                store1.setLatitude(latitude1);
            }
            if (store != null) {
                storeList.add(store);
            }
        }
        if (storeList.size() == 0) {
            return new BigDecimal("0");
        }

        //获取二次中转时长
        Store store1 = storeList.get(0);
        Long accumulationId1 = store1.getAccumulationId();
        Accumulation accumulation = accumulationMapper.selectById(accumulationId1);

        String erciTime = "0";

        if (accumulation != null) {
            Long transitDepotId = accumulation.getTransitDepotId();
            Double time = systemParameterMapper.selectSecondByTranId(transitDepotId);
            if (time != null) {
                erciTime = String.valueOf(time);
            }
        }


        SystemParameter sp = systemParameterMapper.selectById(1);

        //t2
        // 遍历store数组 算出两个store数组之间的距离并除以 相应的速度  接着累加
        BigDecimal totalDistance = new BigDecimal("0");
        List<Double> distanceList = new ArrayList<>();
        BigDecimal t2 = new BigDecimal("0");

        ArrayList<String> list2 = new ArrayList<>();
        for (int i = 0; i < storeList.size(); i++) {
            list2.add(storeList.get(i).getLongitude() + " " + storeList.get(i).getLatitude());
            if (i == storeList.size() - 1) {
                break;
            }
            //Store store = storeList.get(Integer.parseInt(split[i]));
            //Store nextStore = storeList.get(Integer.parseInt(split[i+1]));
            Store store = storeList.get(i);
            Store nextStore = storeList.get(i + 1);

            // 查看两个商铺的类型得   速度
            double speed;
            String locationType = store.getLocationType();
            String locationType1 = nextStore.getLocationType();
            if (!StringUtil.isNotBlank(locationType)) {
                locationType = "1";
            }
            if (!StringUtil.isNotBlank(locationType1)) {
                locationType1 = "1";
            }
            if (locationType.equals(locationType1)) {
                if (locationType.equals("0")) {
                    // 城区 -> 城区
                    speed = sp.getUrbanRoads();
                } else {
                    // 乡镇 -> 乡镇
                    speed = sp.getTownshipRoads();
                }
            } else {
                // 城区 -> 乡镇 或 乡镇 -> 城区
                speed = (sp.getUrbanRoads() + sp.getTownshipRoads()) / 2;
            }

            // 查看俩个商铺的距离
            DoublePoint point1 = new DoublePoint(new double[]{store.getLongitude(), store.getLatitude()});
            DoublePoint point2 = new DoublePoint(new double[]{nextStore.getLongitude(), nextStore.getLatitude()});
            double[] distance = saveDistanceInformation(point1, point2, apiKey);
            distanceList.add(distance[0]);
            totalDistance = totalDistance.add(new BigDecimal(distance[0]));
            // 距离除以速度 并t2累加
            if (distance[1] != 0) {
                speed = sp.getFreeway();
            }
            BigDecimal speedDecimal = new BigDecimal(speed);
            BigDecimal distanceDecimal = new BigDecimal(distance[0]).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP);
            BigDecimal divide = distanceDecimal.divide(speedDecimal, 2, RoundingMode.HALF_UP);
            BigDecimal multiplyHour = divide.multiply(new BigDecimal("60"));
            t2 = t2.add(multiplyHour);
        }
        System.out.println("----------------");
        String join1 = String.join(",", list2);
        System.out.println(join1);

        // T3
        // 统计store集合中accumulationId 计算城区和乡镇的商铺总和
        int cityNum = 0;//城市商铺数量
        int countryNum = 0;//乡村商铺数量

        //最多商铺的密集度系数
        double max = 0;

        List<Long> accumulationIdList = storeList.stream()
                .map(Store::getAccumulationId)
                .distinct()
                .collect(Collectors.toList());
        for (Long accumulationId : accumulationIdList) {
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("accumulation_id", accumulationId);
            List<Store> list = storeMapper.selectList(queryWrapper);
            //统计最大密集度系数
            if (list.size() > max) {
                max = list.size();
            }
        }
        //商铺平均卸货时长
        double cityTime = sp.getShoreUnloadCityTime();//城市
        double townshipTime = sp.getShoreUnloadTownshipTime();//乡镇

 /*       BigDecimal a = new BigDecimal("0");
        BigDecimal b = new BigDecimal("0");*/


        for (Long accumulationId : accumulationIdList) {
            QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("accumulation_id", accumulationId);
            List<Store> list = storeMapper.selectList(queryWrapper);

            for (Store store : list) {
                if ("0".equals(store.getLocationType())) {
                    cityNum++;
                } else {
                    countryNum++;
                }
            }
/*            //计算当前打卡点的密集度系数
            double curr = list.size() /( max * sp.getAccumulationIntensity());
            //计算当前路线的卸货时长
            a = a.add(new BigDecimal(String.valueOf(cityNum * (cityTime / curr))));
            b = b.add(new BigDecimal(String.valueOf(countryNum *( townshipTime / curr))));*/
        }

        System.out.println("坐标点串实际数：" + polylineMap.size());
        System.out.println("实际跟据点串获得聚集区/打卡点数（不含起点终点）：" + storeList.size());
        System.out.println("路线总距离：" + totalDistance);
        System.out.println("路线数据集个数：" + distanceList.size());
        System.out.println("T1(装车时长)：" + 40);
        System.out.println("T2（途中时长）：" + t2);
        System.out.println("城区商铺总数：" + cityNum);
        System.out.println("乡镇商铺总数：" + countryNum);


        BigDecimal a = new BigDecimal(cityNum).multiply(new BigDecimal("2.34"));
        //BigDecimal a = new BigDecimal(cityNum).multiply(new BigDecimal(String.valueOf(cityTime)));
        //后续查询数据库进行填充
        //乡村成功平均卸货时长
        BigDecimal b = new BigDecimal(countryNum).multiply(new BigDecimal("2.9"));
        //BigDecimal b = new BigDecimal(countryNum).multiply(new BigDecimal(String.valueOf(townshipTime)));
        System.out.println("城区商铺时长：" + a);
        System.out.println("乡镇商铺时长：" + b);

        BigDecimal t3 = a.add(b);
        System.out.println("T3（卸货配送时长）：" + t3);
        //加上二次中转时长
        BigDecimal add = t3.add(new BigDecimal(erciTime));


        // T1+T2+T3
        //设置装车时长
        String loadingTime = String.valueOf(sp.getLoadingTime());

        BigDecimal t1 = new BigDecimal(loadingTime);
        BigDecimal t = t1.add(t2).add(add);
        return t;
    }

    //输入班组id，查询该班组的平均工作时长（单位h）
    public double getTeamWorkTime(Long teamId) {
        List<TransitDepot> transitDepots = transitDepotMapper.selectList(new QueryWrapper<TransitDepot>().eq("is_delete", 0).eq("group_id", teamId));
        //查询该班组中的车辆总数
        int routeSum = 0;
        double cworkTimeSum = 0;
        for (TransitDepot transitDepot : transitDepots) {
            List<Route> routes = routeMapper.selectList(new QueryWrapper<Route>().eq("is_delete", 0).eq("transit_depot_id", transitDepot.getTransitDepotId()));
            routeSum += routes.size();
            for (Route route : routes) {
                cworkTimeSum += Double.parseDouble(route.getWorkTime());
            }
        }
        return cworkTimeSum / routeSum / 60;
    }


    //根据路线获取遗传算法的二维数组
    public double[][] getDouble(String path) {
        String[] split = path.split(";");
        double[][] doubles = new double[split.length - 1][2];
        for (int i = 0; i < split.length - 1; i++) {
            String[] split1 = split[i].split(",");
            doubles[i][0] = Double.parseDouble(split1[0]);
            doubles[i][1] = Double.parseDouble(split1[1]);
        }
        return doubles;
    }


    //根据遗传算法结果，重新排序路径
    public String getPath(String path, String[] run) {
        ArrayList<Integer> list = new ArrayList<>();
        if ("1".equals(run[0])) {
            for (String s : run) {
                list.add(Integer.valueOf(s));
            }
        } else {
            int flag = 0;
            for (int i = 0; i < run.length; i++) {
                if ("1".equals(run[i])) {
                    flag = i;
                }
                if (flag != 0) {
                    list.add(Integer.valueOf(run[i]));
                }
            }
            for (int i = 0; i <= flag; i++) {
                list.add(Integer.valueOf(run[i]));
            }
        }
        String[] split = path.split(";");
        ArrayList<String> list1 = new ArrayList<>();
        for (Integer integer : list) {
            list1.add(split[integer - 1]);
        }
        return String.join(";", list1);
    }
}
