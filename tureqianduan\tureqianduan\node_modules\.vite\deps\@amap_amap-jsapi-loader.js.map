{"version": 3, "sources": ["../../@amap/amap-jsapi-loader/dist/index.js"], "sourcesContent": ["'use strict';(function(m,p){\"object\"===typeof exports&&\"undefined\"!==typeof module?module.exports=p():\"function\"===typeof define&&define.amd?define(p):(m=m||self,m.AMapLoader=p())})(this,function(){function m(a){var b=[];a.AMapUI&&b.push(p(a.AMapUI));a.Loca&&b.push(r(a.Loca));return Promise.all(b)}function p(a){return new Promise(function(h,c){var f=[];if(a.plugins)for(var e=0;e<a.plugins.length;e+=1)-1==d.AMapUI.plugins.indexOf(a.plugins[e])&&f.push(a.plugins[e]);if(g.AMapUI===b.failed)c(\"\\u524d\\u6b21\\u8bf7\\u6c42 AMapUI \\u5931\\u8d25\");\nelse if(g.AMapUI===b.notload){g.AMapUI=b.loading;d.AMapUI.version=a.version||d.AMapUI.version;e=d.AMapUI.version;var l=document.body||document.head,k=document.createElement(\"script\");k.type=\"text/javascript\";k.src=\"https://webapi.amap.com/ui/\"+e+\"/main.js\";k.onerror=function(a){g.AMapUI=b.failed;c(\"\\u8bf7\\u6c42 AMapUI \\u5931\\u8d25\")};k.onload=function(){g.AMapUI=b.loaded;if(f.length)window.AMapUI.loadUI(f,function(){for(var a=0,b=f.length;a<b;a++){var c=f[a].split(\"/\").slice(-1)[0];window.AMapUI[c]=\narguments[a]}for(h();n.AMapUI.length;)n.AMapUI.splice(0,1)[0]()});else for(h();n.AMapUI.length;)n.AMapUI.splice(0,1)[0]()};l.appendChild(k)}else g.AMapUI===b.loaded?a.version&&a.version!==d.AMapUI.version?c(\"\\u4e0d\\u5141\\u8bb8\\u591a\\u4e2a\\u7248\\u672c AMapUI \\u6df7\\u7528\"):f.length?window.AMapUI.loadUI(f,function(){for(var a=0,b=f.length;a<b;a++){var c=f[a].split(\"/\").slice(-1)[0];window.AMapUI[c]=arguments[a]}h()}):h():a.version&&a.version!==d.AMapUI.version?c(\"\\u4e0d\\u5141\\u8bb8\\u591a\\u4e2a\\u7248\\u672c AMapUI \\u6df7\\u7528\"):\nn.AMapUI.push(function(a){a?c(a):f.length?window.AMapUI.loadUI(f,function(){for(var a=0,b=f.length;a<b;a++){var c=f[a].split(\"/\").slice(-1)[0];window.AMapUI[c]=arguments[a]}h()}):h()})})}function r(a){return new Promise(function(h,c){if(g.Loca===b.failed)c(\"\\u524d\\u6b21\\u8bf7\\u6c42 Loca \\u5931\\u8d25\");else if(g.Loca===b.notload){g.Loca=b.loading;d.Loca.version=a.version||d.Loca.version;var f=d.Loca.version,e=d.AMap.version.startsWith(\"2\"),l=f.startsWith(\"2\");if(e&&!l||!e&&l)c(\"JSAPI \\u4e0e Loca \\u7248\\u672c\\u4e0d\\u5bf9\\u5e94\\uff01\\uff01\");\nelse{e=d.key;l=document.body||document.head;var k=document.createElement(\"script\");k.type=\"text/javascript\";k.src=\"https://webapi.amap.com/loca?v=\"+f+\"&key=\"+e;k.onerror=function(a){g.Loca=b.failed;c(\"\\u8bf7\\u6c42 AMapUI \\u5931\\u8d25\")};k.onload=function(){g.Loca=b.loaded;for(h();n.Loca.length;)n.Loca.splice(0,1)[0]()};l.appendChild(k)}}else g.Loca===b.loaded?a.version&&a.version!==d.Loca.version?c(\"\\u4e0d\\u5141\\u8bb8\\u591a\\u4e2a\\u7248\\u672c Loca \\u6df7\\u7528\"):h():a.version&&a.version!==d.Loca.version?\nc(\"\\u4e0d\\u5141\\u8bb8\\u591a\\u4e2a\\u7248\\u672c Loca \\u6df7\\u7528\"):n.Loca.push(function(a){a?c(a):c()})})}if(!window)throw Error(\"AMap JSAPI can only be used in Browser.\");var b;(function(a){a.notload=\"notload\";a.loading=\"loading\";a.loaded=\"loaded\";a.failed=\"failed\"})(b||(b={}));var d={key:\"\",AMap:{version:\"1.4.15\",plugins:[]},AMapUI:{version:\"1.1\",plugins:[]},Loca:{version:\"1.3.2\"}},g={AMap:b.notload,AMapUI:b.notload,Loca:b.notload},n={AMap:[],AMapUI:[],Loca:[]},q=[],t=function(a){\"function\"==typeof a&&\n(g.AMap===b.loaded?a(window.AMap):q.push(a))};return{load:function(a){return new Promise(function(h,c){if(g.AMap==b.failed)c(\"\");else if(g.AMap==b.notload){var f=a.key,e=a.version,l=a.plugins;f?(window.AMap&&\"lbs.amap.com\"!==location.host&&c(\"\\u7981\\u6b62\\u591a\\u79cdAPI\\u52a0\\u8f7d\\u65b9\\u5f0f\\u6df7\\u7528\"),d.key=f,d.AMap.version=e||d.AMap.version,d.AMap.plugins=l||d.AMap.plugins,g.AMap=b.loading,e=document.body||document.head,window.___onAPILoaded=function(d){delete window.___onAPILoaded;if(d)g.AMap=\nb.failed,c(d);else for(g.AMap=b.loaded,m(a).then(function(){h(window.AMap)})[\"catch\"](c);q.length;)q.splice(0,1)[0]()},l=document.createElement(\"script\"),l.type=\"text/javascript\",l.src=\"https://webapi.amap.com/maps?callback=___onAPILoaded&v=\"+d.AMap.version+\"&key=\"+f+\"&plugin=\"+d.AMap.plugins.join(\",\"),l.onerror=function(a){g.AMap=b.failed;c(a)},e.appendChild(l)):c(\"\\u8bf7\\u586b\\u5199key\")}else if(g.AMap==b.loaded)if(a.key&&a.key!==d.key)c(\"\\u591a\\u4e2a\\u4e0d\\u4e00\\u81f4\\u7684 key\");else if(a.version&&\na.version!==d.AMap.version)c(\"\\u4e0d\\u5141\\u8bb8\\u591a\\u4e2a\\u7248\\u672c JSAPI \\u6df7\\u7528\");else{f=[];if(a.plugins)for(e=0;e<a.plugins.length;e+=1)-1==d.AMap.plugins.indexOf(a.plugins[e])&&f.push(a.plugins[e]);if(f.length)window.AMap.plugin(f,function(){m(a).then(function(){h(window.AMap)})[\"catch\"](c)});else m(a).then(function(){h(window.AMap)})[\"catch\"](c)}else if(a.key&&a.key!==d.key)c(\"\\u591a\\u4e2a\\u4e0d\\u4e00\\u81f4\\u7684 key\");else if(a.version&&a.version!==d.AMap.version)c(\"\\u4e0d\\u5141\\u8bb8\\u591a\\u4e2a\\u7248\\u672c JSAPI \\u6df7\\u7528\");\nelse{var k=[];if(a.plugins)for(e=0;e<a.plugins.length;e+=1)-1==d.AMap.plugins.indexOf(a.plugins[e])&&k.push(a.plugins[e]);t(function(){if(k.length)window.AMap.plugin(k,function(){m(a).then(function(){h(window.AMap)})[\"catch\"](c)});else m(a).then(function(){h(window.AMap)})[\"catch\"](c)})}})},reset:function(){delete window.AMap;delete window.AMapUI;delete window.Loca;d={key:\"\",AMap:{version:\"1.4.15\",plugins:[]},AMapUI:{version:\"1.1\",plugins:[]},Loca:{version:\"1.3.2\"}};g={AMap:b.notload,AMapUI:b.notload,\nLoca:b.notload};n={AMap:[],AMapUI:[],Loca:[]}}}})\n"], "mappings": ";;;;;AAAA;AAAA;AAAa,KAAC,SAAS,GAAE,GAAE;AAAC,mBAAW,OAAO,WAAS,gBAAc,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,eAAa,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,KAAG,MAAK,EAAE,aAAW,EAAE;AAAA,IAAE,GAAG,SAAK,WAAU;AAAC,eAAS,EAAE,GAAE;AAAC,YAAIA,KAAE,CAAC;AAAE,UAAE,UAAQA,GAAE,KAAK,EAAE,EAAE,MAAM,CAAC;AAAE,UAAE,QAAMA,GAAE,KAAK,EAAE,EAAE,IAAI,CAAC;AAAE,eAAO,QAAQ,IAAIA,EAAC;AAAA,MAAC;AAAC,eAAS,EAAE,GAAE;AAAC,eAAO,IAAI,QAAQ,SAAS,GAAE,GAAE;AAAC,cAAI,IAAE,CAAC;AAAE,cAAG,EAAE,QAAQ,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAQ,QAAO,KAAG,EAAE,OAAI,EAAE,OAAO,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,KAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAAE,cAAG,EAAE,WAAS,EAAE,OAAO,GAAE,gBAA8C;AAAA,mBACphB,EAAE,WAAS,EAAE,SAAQ;AAAC,cAAE,SAAO,EAAE;AAAQ,cAAE,OAAO,UAAQ,EAAE,WAAS,EAAE,OAAO;AAAQ,gBAAE,EAAE,OAAO;AAAQ,gBAAI,IAAE,SAAS,QAAM,SAAS,MAAK,IAAE,SAAS,cAAc,QAAQ;AAAE,cAAE,OAAK;AAAkB,cAAE,MAAI,gCAA8B,IAAE;AAAW,cAAE,UAAQ,SAASC,IAAE;AAAC,gBAAE,SAAO,EAAE;AAAO,gBAAE,cAAkC;AAAA,YAAC;AAAE,cAAE,SAAO,WAAU;AAAC,gBAAE,SAAO,EAAE;AAAO,kBAAG,EAAE,OAAO,QAAO,OAAO,OAAO,GAAE,WAAU;AAAC,yBAAQA,KAAE,GAAED,KAAE,EAAE,QAAOC,KAAED,IAAEC,MAAI;AAAC,sBAAIC,KAAE,EAAED,EAAC,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC;AAAE,yBAAO,OAAOC,EAAC,IACtf,UAAUD,EAAC;AAAA,gBAAC;AAAC,qBAAI,EAAE,GAAE,EAAE,OAAO,SAAQ,GAAE,OAAO,OAAO,GAAE,CAAC,EAAE,CAAC,EAAE;AAAA,cAAC,CAAC;AAAA,kBAAO,MAAI,EAAE,GAAE,EAAE,OAAO,SAAQ,GAAE,OAAO,OAAO,GAAE,CAAC,EAAE,CAAC,EAAE;AAAA,YAAC;AAAE,cAAE,YAAY,CAAC;AAAA,UAAC,MAAM,GAAE,WAAS,EAAE,SAAO,EAAE,WAAS,EAAE,YAAU,EAAE,OAAO,UAAQ,EAAE,mBAAgE,IAAE,EAAE,SAAO,OAAO,OAAO,OAAO,GAAE,WAAU;AAAC,qBAAQA,KAAE,GAAED,KAAE,EAAE,QAAOC,KAAED,IAAEC,MAAI;AAAC,kBAAIC,KAAE,EAAED,EAAC,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC;AAAE,qBAAO,OAAOC,EAAC,IAAE,UAAUD,EAAC;AAAA,YAAC;AAAC,cAAE;AAAA,UAAC,CAAC,IAAE,EAAE,IAAE,EAAE,WAAS,EAAE,YAAU,EAAE,OAAO,UAAQ,EAAE,mBAAgE,IACjhB,EAAE,OAAO,KAAK,SAASA,IAAE;AAAC,YAAAA,KAAE,EAAEA,EAAC,IAAE,EAAE,SAAO,OAAO,OAAO,OAAO,GAAE,WAAU;AAAC,uBAAQA,KAAE,GAAED,KAAE,EAAE,QAAOC,KAAED,IAAEC,MAAI;AAAC,oBAAIC,KAAE,EAAED,EAAC,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC;AAAE,uBAAO,OAAOC,EAAC,IAAE,UAAUD,EAAC;AAAA,cAAC;AAAC,gBAAE;AAAA,YAAC,CAAC,IAAE,EAAE;AAAA,UAAC,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAC,eAAS,EAAE,GAAE;AAAC,eAAO,IAAI,QAAQ,SAAS,GAAE,GAAE;AAAC,cAAG,EAAE,SAAO,EAAE,OAAO,GAAE,cAA4C;AAAA,mBAAU,EAAE,SAAO,EAAE,SAAQ;AAAC,cAAE,OAAK,EAAE;AAAQ,cAAE,KAAK,UAAQ,EAAE,WAAS,EAAE,KAAK;AAAQ,gBAAI,IAAE,EAAE,KAAK,SAAQ,IAAE,EAAE,KAAK,QAAQ,WAAW,GAAG,GAAE,IAAE,EAAE,WAAW,GAAG;AAAE,gBAAG,KAAG,CAAC,KAAG,CAAC,KAAG,EAAE,GAAE,sBAA8D;AAAA,iBAC3hB;AAAC,kBAAE,EAAE;AAAI,kBAAE,SAAS,QAAM,SAAS;AAAK,kBAAI,IAAE,SAAS,cAAc,QAAQ;AAAE,gBAAE,OAAK;AAAkB,gBAAE,MAAI,oCAAkC,IAAE,UAAQ;AAAE,gBAAE,UAAQ,SAASA,IAAE;AAAC,kBAAE,OAAK,EAAE;AAAO,kBAAE,cAAkC;AAAA,cAAC;AAAE,gBAAE,SAAO,WAAU;AAAC,kBAAE,OAAK,EAAE;AAAO,qBAAI,EAAE,GAAE,EAAE,KAAK,SAAQ,GAAE,KAAK,OAAO,GAAE,CAAC,EAAE,CAAC,EAAE;AAAA,cAAC;AAAE,gBAAE,YAAY,CAAC;AAAA,YAAC;AAAA,UAAC,MAAM,GAAE,SAAO,EAAE,SAAO,EAAE,WAAS,EAAE,YAAU,EAAE,KAAK,UAAQ,EAAE,iBAA8D,IAAE,EAAE,IAAE,EAAE,WAAS,EAAE,YAAU,EAAE,KAAK,UACpf,EAAE,iBAA8D,IAAE,EAAE,KAAK,KAAK,SAASA,IAAE;AAAC,YAAAA,KAAE,EAAEA,EAAC,IAAE,EAAE;AAAA,UAAC,CAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAC,UAAG,CAAC,OAAO,OAAM,MAAM,yCAAyC;AAAE,UAAI;AAAE,OAAC,SAAS,GAAE;AAAC,UAAE,UAAQ;AAAU,UAAE,UAAQ;AAAU,UAAE,SAAO;AAAS,UAAE,SAAO;AAAA,MAAQ,GAAG,MAAI,IAAE,CAAC,EAAE;AAAE,UAAI,IAAE,EAAC,KAAI,IAAG,MAAK,EAAC,SAAQ,UAAS,SAAQ,CAAC,EAAC,GAAE,QAAO,EAAC,SAAQ,OAAM,SAAQ,CAAC,EAAC,GAAE,MAAK,EAAC,SAAQ,QAAO,EAAC,GAAE,IAAE,EAAC,MAAK,EAAE,SAAQ,QAAO,EAAE,SAAQ,MAAK,EAAE,QAAO,GAAE,IAAE,EAAC,MAAK,CAAC,GAAE,QAAO,CAAC,GAAE,MAAK,CAAC,EAAC,GAAE,IAAE,CAAC,GAAE,IAAE,SAAS,GAAE;AAAC,sBAAY,OAAO,MACxf,EAAE,SAAO,EAAE,SAAO,EAAE,OAAO,IAAI,IAAE,EAAE,KAAK,CAAC;AAAA,MAAE;AAAE,aAAM,EAAC,MAAK,SAAS,GAAE;AAAC,eAAO,IAAI,QAAQ,SAAS,GAAE,GAAE;AAAC,cAAG,EAAE,QAAM,EAAE,OAAO,GAAE,EAAE;AAAA,mBAAU,EAAE,QAAM,EAAE,SAAQ;AAAC,gBAAI,IAAE,EAAE,KAAI,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAQ,iBAAG,OAAO,QAAM,mBAAiB,SAAS,QAAM,EAAE,eAAiE,GAAE,EAAE,MAAI,GAAE,EAAE,KAAK,UAAQ,KAAG,EAAE,KAAK,SAAQ,EAAE,KAAK,UAAQ,KAAG,EAAE,KAAK,SAAQ,EAAE,OAAK,EAAE,SAAQ,IAAE,SAAS,QAAM,SAAS,MAAK,OAAO,iBAAe,SAASE,IAAE;AAAC,qBAAO,OAAO;AAAe,kBAAGA,GAAE,GAAE,OACrf,EAAE,QAAO,EAAEA,EAAC;AAAA,kBAAO,MAAI,EAAE,OAAK,EAAE,QAAO,EAAE,CAAC,EAAE,KAAK,WAAU;AAAC,kBAAE,OAAO,IAAI;AAAA,cAAC,CAAC,EAAE,OAAO,EAAE,CAAC,GAAE,EAAE,SAAQ,GAAE,OAAO,GAAE,CAAC,EAAE,CAAC,EAAE;AAAA,YAAC,GAAE,IAAE,SAAS,cAAc,QAAQ,GAAE,EAAE,OAAK,mBAAkB,EAAE,MAAI,4DAA0D,EAAE,KAAK,UAAQ,UAAQ,IAAE,aAAW,EAAE,KAAK,QAAQ,KAAK,GAAG,GAAE,EAAE,UAAQ,SAASF,IAAE;AAAC,gBAAE,OAAK,EAAE;AAAO,gBAAEA,EAAC;AAAA,YAAC,GAAE,EAAE,YAAY,CAAC,KAAG,EAAE,QAAuB;AAAA,UAAC,WAAS,EAAE,QAAM,EAAE,OAAO,KAAG,EAAE,OAAK,EAAE,QAAM,EAAE,IAAI,GAAE,YAA0C;AAAA,mBAAU,EAAE,WAClf,EAAE,YAAU,EAAE,KAAK,QAAQ,GAAE,kBAA+D;AAAA,eAAM;AAAC,gBAAE,CAAC;AAAE,gBAAG,EAAE,QAAQ,MAAI,IAAE,GAAE,IAAE,EAAE,QAAQ,QAAO,KAAG,EAAE,OAAI,EAAE,KAAK,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,KAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAAE,gBAAG,EAAE,OAAO,QAAO,KAAK,OAAO,GAAE,WAAU;AAAC,gBAAE,CAAC,EAAE,KAAK,WAAU;AAAC,kBAAE,OAAO,IAAI;AAAA,cAAC,CAAC,EAAE,OAAO,EAAE,CAAC;AAAA,YAAC,CAAC;AAAA,gBAAO,GAAE,CAAC,EAAE,KAAK,WAAU;AAAC,gBAAE,OAAO,IAAI;AAAA,YAAC,CAAC,EAAE,OAAO,EAAE,CAAC;AAAA,UAAC;AAAA,mBAAS,EAAE,OAAK,EAAE,QAAM,EAAE,IAAI,GAAE,YAA0C;AAAA,mBAAU,EAAE,WAAS,EAAE,YAAU,EAAE,KAAK,QAAQ,GAAE,kBAA+D;AAAA,eACjiB;AAAC,gBAAI,IAAE,CAAC;AAAE,gBAAG,EAAE,QAAQ,MAAI,IAAE,GAAE,IAAE,EAAE,QAAQ,QAAO,KAAG,EAAE,OAAI,EAAE,KAAK,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,KAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAAE,cAAE,WAAU;AAAC,kBAAG,EAAE,OAAO,QAAO,KAAK,OAAO,GAAE,WAAU;AAAC,kBAAE,CAAC,EAAE,KAAK,WAAU;AAAC,oBAAE,OAAO,IAAI;AAAA,gBAAC,CAAC,EAAE,OAAO,EAAE,CAAC;AAAA,cAAC,CAAC;AAAA,kBAAO,GAAE,CAAC,EAAE,KAAK,WAAU;AAAC,kBAAE,OAAO,IAAI;AAAA,cAAC,CAAC,EAAE,OAAO,EAAE,CAAC;AAAA,YAAC,CAAC;AAAA,UAAC;AAAA,QAAC,CAAC;AAAA,MAAC,GAAE,OAAM,WAAU;AAAC,eAAO,OAAO;AAAK,eAAO,OAAO;AAAO,eAAO,OAAO;AAAK,YAAE,EAAC,KAAI,IAAG,MAAK,EAAC,SAAQ,UAAS,SAAQ,CAAC,EAAC,GAAE,QAAO,EAAC,SAAQ,OAAM,SAAQ,CAAC,EAAC,GAAE,MAAK,EAAC,SAAQ,QAAO,EAAC;AAAE,YAAE;AAAA,UAAC,MAAK,EAAE;AAAA,UAAQ,QAAO,EAAE;AAAA,UAClf,MAAK,EAAE;AAAA,QAAO;AAAE,YAAE,EAAC,MAAK,CAAC,GAAE,QAAO,CAAC,GAAE,MAAK,CAAC,EAAC;AAAA,MAAC,EAAC;AAAA,IAAC,CAAC;AAAA;AAAA;", "names": ["b", "a", "c", "d"]}