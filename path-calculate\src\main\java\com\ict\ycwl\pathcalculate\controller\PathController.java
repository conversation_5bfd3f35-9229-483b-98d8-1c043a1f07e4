package com.ict.ycwl.pathcalculate.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.pathcalculate.AOP.ConfigFileMothodDataSource;
import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import com.ict.ycwl.pathcalculate.common.exception.CarNumberException;
import com.ict.ycwl.pathcalculate.form.AddRouteFrom;
import com.ict.ycwl.pathcalculate.form.AdjustPointForm;
import com.ict.ycwl.pathcalculate.form.GetColourConvexHullFrom;
import com.ict.ycwl.pathcalculate.form.RouteDataForm;
import com.ict.ycwl.pathcalculate.mapper.AccumulationMapper;
import com.ict.ycwl.pathcalculate.mapper.GroupMapper;
import com.ict.ycwl.pathcalculate.mapper.StoreMapper;
import com.ict.ycwl.pathcalculate.mapper.TransitDepotMapper;
import com.ict.ycwl.pathcalculate.pojo.*;
import com.ict.ycwl.pathcalculate.service.CalculateService;
import com.ict.ycwl.pathcalculate.service.MapDisplayService;
import com.ict.ycwl.pathcalculate.service.NewAlgorithmService;
import com.ict.ycwl.pathcalculate.service.RouteService;
import com.ict.ycwl.pathcalculate.service.WorkParameterService;
import com.ict.ycwl.pathcalculate.service.Impl.RouteServiceImpl;
import com.ict.ycwl.pathcalculate.vo.*;
import com.ict.ycwl.pathcalculate.vo.details.AccumulationDetailsVO;
import com.ict.ycwl.pathcalculate.vo.details.StoreDetailsVO;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(tags = "路径计算API")
@Slf4j
@Validated
@RestController
@RequestMapping("/path")
@RequiredArgsConstructor
// @ConfigFileDataSource(configKey ="jjking.MybatisFile")
public class PathController {
    @Autowired
    private CalculateService calculateService;

    @Autowired
    private NewAlgorithmService newAlgorithmService;

    @Autowired
    private RouteService routeService;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private TransitDepotMapper transitDepotMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private AccumulationMapper accumulationMapper;

    @Autowired
    private WorkParameterService workParameterService;

    private final MapDisplayService mapDisplayService;


    @ApiOperation(value = "计算全部中转站接口")
    @GetMapping("/calculateAll")
    public Object calculateAll(@ApiParam(value = "调用api的Key", required = true, defaultValue = "a123fae9da370c45984c58720bf3ac7c")
                               @RequestParam("apiKey") String apiKey,
                               @ApiParam(value = "是否使用新算法", required = false, defaultValue = "true")
                               @RequestParam(value = "useNewAlgorithm", required = false, defaultValue = "true") Boolean useNewAlgorithm) throws ApiKeyException {
        apiKey="a123fae9da370c45984c58720bf3ac7c";
        List<ResultRoute> routes = null;

        try {
            // ===== 参考PathPlanningUtilsTest流程 =====
            log.info("🎯 强制使用新算法执行路径规划（参考PathPlanningUtilsTest流程）");
            log.info("算法选择参数: useNewAlgorithm={}", useNewAlgorithm);

            boolean isNewAlgorithmAvailable = newAlgorithmService.isNewAlgorithmAvailable();
            log.info("新算法可用性检查: {}", isNewAlgorithmAvailable);

            if (!isNewAlgorithmAvailable) {
                log.error("❌ 新算法不可用，请检查配置和组件注入");
                return AjaxResult.error("新算法不可用，请检查系统配置");
            }

            log.info("✅ 使用新算法执行路径规划");
            routes = newAlgorithmService.executeNewAlgorithm(apiKey);
        } catch (CarNumberException | ApiKeyException e) {
            log.error("新算法执行失败: {}", e.getMessage());
            return AjaxResult.error("新算法执行失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("新算法执行异常", e);
            return AjaxResult.error("新算法执行失败: " + e.getMessage());
        }

        if (routes != null && routes.size() != 0) {
            return AjaxResult.success("操作成功", routes);
        } else {
            return AjaxResult.error("操作失败");
        }
    }

    @ApiOperation(value = "计算单个中转站接口")
    @GetMapping("/calculateOne")
    public Object calculateOne(@ApiParam(value = "计算的中转站的名字", required = true, example = "仁化")
                               @RequestParam("areaName") String areaName,
                               @ApiParam(value = "调用api的Key", required = true)
                               @RequestParam("apiKey") String apiKey,
                               @ApiParam(value = "计算的路线数量", required = true, example = "10")
                               @RequestParam("assignNumber") int assignNumber) {
        List<ResultRoute> routes = null;
        try {
            routes = calculateService.calculateOne(areaName, apiKey, assignNumber);
        } catch (CarNumberException | ApiKeyException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (routes.size() != 0) {
            return AjaxResult.success("操作成功", routes);
        } else {
            return AjaxResult.success("操作失败");
        }
    }

    @ApiOperation(value = "获取地图所有班组分割线")
    @GetMapping("/getSplitLines")
    public Object getSplitLines(@ApiParam(value = "班组绕圈顺序", required = true, example = "二四五一三六")
                                @RequestParam("groupOrder") String groupOrder) {
        return AjaxResult.success(mapDisplayService.getSplitLines(groupOrder));
    }

    @ApiOperation(value = "获取单个班组着色分块")
    @PostMapping("/getColourConvexHull")
    public Object getColourConvexHull(@Validated
                                      @RequestBody GetColourConvexHullFrom requestParam) {
        List<List<LngAndLat>> result = null;
        try {
            result = mapDisplayService.getColourConvexHull(requestParam);
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("班组不存在");
        }
        return AjaxResult.success(result);
    }

    @ApiOperation(value = "获取所有凸包的着色分块")
    @GetMapping("/getAllColourConvex")
    public Object getAllColourConvex() throws Exception {
        List<ArrayList<String>> result = null;
       // try {
            result = mapDisplayService.getAllColourConvex();
        //} catch (Exception e) {
            //return AjaxResult.error(e.getMessage());
       // }
        return AjaxResult.success(result);
    }

    @ApiOperation(value = "获取所有凸包打卡点")
    @GetMapping("/getConvexPoint")
    public Object getConvexPoint() {
        return AjaxResult.success(mapDisplayService.getConvexPoint());
    }

    @ApiOperation(value = "获取启用的所有中转站名字")
    @GetMapping("/getTransitDepotName")
    public Object getTransitDepotName() {
        return AjaxResult.success(mapDisplayService.getTransitDepotName());
    }

    @ApiOperation(value = "调整后的路径的重新计算")
    @GetMapping("/calculateRangedRoute")
    public Object calculateRangedRoute(@ApiParam(value = "计算的路径1的名字", required = true)
                                       @RequestParam("routeName1") String routeName1,
                                       @ApiParam(value = "计算的路径2的名字", required = true)
                                       @RequestParam("routeName2") String routeName2,
                                       @ApiParam(value = "调用api的Key", required = true)
                                       @RequestParam("apiKey") String apiKey) {
        List<ResultRoute> resultRoutes = null;
        try {
            resultRoutes = calculateService.calculateRangedRoute(routeName1, routeName2, apiKey);
        } catch (ApiKeyException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (resultRoutes.size() != 0) {
            return AjaxResult.success("操作成功", resultRoutes);
        } else {
            return AjaxResult.success("操作失败");
        }
    }

    //获取凸包数据
    @ApiOperation(value = "获取所有已保存路线数据")
    @GetMapping("/getConvex")
    public Object getConvex(@ApiParam(value = "调用api的Key", required = true)
                            @RequestParam("apiKey") String apiKey) {
        List<ResultRoute> convex = null;
        try {
            convex = mapDisplayService.getConvex(apiKey);
        } catch (ApiKeyException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success(convex);
    }

    /**
     * 获取路线详情，包含大区、路线、聚集区
     *
     * @return vo
     */
    @ApiOperation(value = "获取路线详情-班组中转站路线信息")
    @GetMapping("/getRouteDetails")
    public AjaxResult getRouteDetails() {
        List<GroupDataVO> routeDetails = routeService.getRouteDetails();
        return AjaxResult.success(routeDetails);
    }

    /**
     * 获取路线下聚集区
     *
     * @return vo
     */
    @ApiOperation(value = "获取路线详情-聚集区信息")
    @GetMapping("/getAccumulationDetails/{routeId}")
    public AjaxResult getAccumulationDetails(@PathVariable String routeId) {
        List<AccumulationDetailsVO> result = routeService.getAccumulationDetails(Long.parseLong(routeId));
        return AjaxResult.success(result);
    }

    /**
     * 获取聚集区下商户信息
     *
     * @return vo
     */
    @ApiOperation(value = "获取路线详情-聚集区下商户信息")
    @ApiParam(name = "accumulationId", value = "聚集区ID", example = "1749295912216735746", required = true)
    @GetMapping("/getStoreDetails/{accumulationId}")
    public AjaxResult getStoreDetails(@PathVariable String accumulationId) {
        List<StoreDetailsVO> storeDetails = routeService.getStoreDetails(Long.parseLong(accumulationId));
        return AjaxResult.success(storeDetails);
    }

    /**
     * 获取路线列表
     *
     * @return ar
     */
    /*@ApiOperation(value = "获取路线列表")
    @GetMapping("/getRouteList")
    public AjaxResult getRouteList() {
        List<RouteVO> routeList = routeService.getRouteList();
        return AjaxResult.success(routeList);
    }*/

    /**
     * 获取地图数据（外层）
     *
     * @return ar
     */
    @ApiOperation(value = "获取地图数据")
    @GetMapping("/getMapData")
    public AjaxResult getMapData() {
        List<RouteVO> mapData = routeService.getMapData();
        return AjaxResult.success(mapData);
    }

    /**
     * 获取中转站历史路径列表数据
     *
     * @return ar
     */
    @ApiOperation(value = "路径分析-获取中转站历史路径列表数据")
    @GetMapping("/getTransitDepotRouteData")
    public AjaxResult getAreaRouteData() {
        List<TransitDepotRouteVO> areaRouteData = routeService.getTransitDepotRouteData();
        return AjaxResult.success(areaRouteData);
    }

    /**
     * 获取路径详细数据
     *
     * @return ar
     */
    @ApiOperation(value = "路径分析-获取路径详细数据")
    @GetMapping("/getRouteData/{apiKey}")
    public AjaxResult getRouteData(@Valid RouteDataForm form, @PathVariable("apiKey") String apiKey) throws ApiKeyException {
        RouteDataVO routeData = routeService.getRouteData(form.getTransitDepotId(), form.getRouteName(), apiKey);
        if (routeData == null) {
            return AjaxResult.error("路径不存在，请重试");
        }
        return AjaxResult.success(routeData);
    }

    /**
     * 保存路径
     *
     * @return ar
     */
    @ApiOperation(value = "路径分析-保存路径")
    @PostMapping("/addRoute")
    public AjaxResult addRoute(@Validated @RequestBody List<AddRouteFrom> from) throws ApiKeyException {
        try {
            routeService.addRoute(from);
        } catch (Exception e) {
            log.error(e.getMessage());
            return AjaxResult.error("参数有误，请重试");
        }
        //调用班组平衡计算
        //inTeamAveTime("3729e38b382749ba3a10bae7539e0d9a");
        //班组间平衡
        //optimizingOneGroupRoute("3729e38b382749ba3a10bae7539e0d9a");
        //先班组内平衡
        //inTeamAveTime("3729e38b382749ba3a10bae7539e0d9a");
        return AjaxResult.success("保存成功");
    }

    /**
     * 获取路径版本号
     *
     * @param date
     * @param transitDepotId
     * @return
     */
    @ApiOperation(value = "路径分析-获取路径版本号")
    @GetMapping("/getRouteVersion")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "日期", name = "date", type = "String", required = true, example = "2024-03-10"),
            @ApiImplicitParam(value = "中转站ID", name = "transitDepotId", type = "Long", required = true, example = "8")
    })
    public AjaxResult getRouteVersion(String date, Long transitDepotId) {
        List<String> routeVersion = routeService.getRouteVersion(date, transitDepotId);
        return AjaxResult.success(routeVersion);
    }


    /**
     * 路径比较-New
     *
     * @param routeIdList
     * @return
     */
    @ApiOperation(value = "路径比较")
    @GetMapping("/compare/{routeIdList}/{apiKey}")
    @ApiParam(name = "routeIdList", value = "大区Id集合", type = "List<Long>", required = true)
    public AjaxResult compare(@PathVariable("routeIdList") List<Long> routeIdList, @PathVariable("apiKey") String apiKey) throws ApiKeyException {
        List<RouteDataVO> list = new ArrayList<>();
        for (Long id : routeIdList) {
            Route route = routeService.getById(id);
            RouteDataVO routeDataVO = routeService.getRouteDataVO(route, apiKey);
            routeDataVO.setWorkTime(new BigDecimal(routeDataVO.getWorkTime()).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP).toString());
            list.add(routeDataVO);
        }
        return AjaxResult.success(list);
    }

    /**
     * 调整打卡点所属路径或大区
     *
     * @param adjustPointForm
     * @return
     */
    @ApiOperation("调整打卡点所属路径")
    @PostMapping("/adjustPoint")
    public AjaxResult adjustPoint(@RequestBody AdjustPointForm adjustPointForm) {
        Long accumulationId = adjustPointForm.getAccumulationId();
        Long routeId = adjustPointForm.getRouteId();
        Long areaId = routeService.getById(routeId).getAreaId();

        // 参数校验
        if (accumulationId == null && routeId == null) {
            return AjaxResult.error("参数有误");
        }

        // 修改聚集区的路线Id和行政区Id
        Accumulation accumulation = new Accumulation();
        accumulation.setRouteId(routeId);
        accumulation.setAreaId(areaId);
        accumulation.setAccumulationId(accumulationId);
        accumulationMapper.updateById(accumulation);

        // 修改聚集区下的商铺的行政区Id
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("accumulation_id", accumulationId);
        List<Long> collect = storeMapper.selectList(queryWrapper).stream().map(Store::getStoreId).collect(Collectors.toList());
        for (Long storeId : collect) {
            Store build = Store.builder().storeId(storeId).areaId(areaId).build();
            storeMapper.updateById(build);
        }

        return AjaxResult.success("操作成功");
    }

    @ApiOperation("班组比较")
    @GetMapping("/compareBaseGroup/{groupIdList}/{apiKey}")
    @ConfigFileMothodDataSource(configKey = "jjking.dbPath")
    public AjaxResult compareBaseGroup(@PathVariable("groupIdList") Long[] groupIdList, @PathVariable("apiKey") String apiKey) throws ApiKeyException {
        List<GroupRouteVO> result = new ArrayList<>();

        for (Long groupId : groupIdList) {
            // 查询中转站集合
            QueryWrapper<TransitDepot> tdQueryWrapper = new QueryWrapper<>();
            tdQueryWrapper.eq("group_id", groupId);
            List<TransitDepot> transitDepots = transitDepotMapper.selectList(tdQueryWrapper);
            List<Long> transitDepotIds = transitDepots.stream().map(TransitDepot::getTransitDepotId).collect(Collectors.toList());

            // 本班组累积数据
            BigDecimal totalDistance = new BigDecimal("0");
            BigDecimal totalWorkTime = new BigDecimal("0");
            BigDecimal totalCargoWeight = new BigDecimal("0");

            int routeSize = 0;
            //累计该班组下的所有路线的数据
            for (Long transitDepotId : transitDepotIds) {
                // 获取大区下所有路线
                QueryWrapper<Route> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("transit_depot_id", transitDepotId);
                queryWrapper.eq("is_delete", 0);
                List<Route> routeList = routeService.list(queryWrapper);
                // 获取每个路线数据
                for (Route route : routeList) {
                    // 使用轻量级方法避免复杂的实时计算
                    RouteDataVO routeDataVO = ((RouteServiceImpl) routeService).getRouteDataVOForComparison(route);
                    // 计算大区下所有路线数据累积
                    String distance = routeDataVO.getDistance();
                    String workTime = routeDataVO.getWorkTime();
                    String cargoWeight = routeDataVO.getCargoWeight();

                    // 添加null检查，避免NumberFormatException
                    if (distance != null && !distance.isEmpty()) {
                        BigDecimal routeDistance = new BigDecimal(distance);//距离
                        totalDistance = totalDistance.add(routeDistance);//累加
                    }

                    if (workTime != null && !workTime.isEmpty()) {
                        totalWorkTime = totalWorkTime.add(new BigDecimal(workTime));//时间
                    }

                    if (cargoWeight != null && !cargoWeight.isEmpty()) {
                        BigDecimal routeCargoWeight = new BigDecimal(cargoWeight);//载货量
                        totalCargoWeight = totalCargoWeight.add(routeCargoWeight);
                    }

                    routeSize++;
                }
            }
            //如果该班组没有任何数据，则返回一个空数据的对象，并跳过该班组
            if (routeSize == 0) {
                result.add(new GroupRouteVO());
                continue;
            }
            BigDecimal size = new BigDecimal(routeSize);
            //每条路径要多少工作时间（分钟），
            BigDecimal averageWorkTime = totalWorkTime.divide(size, 2, RoundingMode.HALF_UP).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
            //每条路径的平均载货量
            BigDecimal averCargoWeight = totalCargoWeight.divide(size, 2, RoundingMode.HALF_UP);
            //每条路径的平均距离
            BigDecimal averageDistance = (totalDistance.divide(size, 2, RoundingMode.HALF_UP));

            //如果当前班组不存在
            Group group = groupMapper.selectById(groupId);
            if(group==null){
                continue;
            }
            // 获取groupName
            String groupName = group.getGroupName();

            GroupRouteVO groupRouteVO = GroupRouteVO.builder().groupId(groupId).groupName(groupName)
                    .averageWorkTime(averageWorkTime.toString())
                    .averCargoWeight(averCargoWeight.toString())
                    .averageDistance(averageDistance.toString())
                    .totalCargoWeight(totalCargoWeight.toString()).build();
            result.add(groupRouteVO);
        }
        return AjaxResult.success(result);

    }

    @ApiOperation("查询所有班组的id，用户班组比较接口的参数")
    @GetMapping("queryGroupAllId")
    public AjaxResult queryGroupAllId(){
       return  routeService.queryGroupAllId();
    }



    @ApiOperation(value = "单个班组路径优化-目的减少工作时长",tags = "a123fae9da370c45984c58720bf3ac7c")
    @GetMapping("/optimizingOneGroupRoute")
    public AjaxResult optimizingOneGroupRoute( String apikey) throws ApiKeyException {
        // 添加重试机制，处理数据库锁异常
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                String info = routeService.optimizingOneGroupRoute(apikey);
                //inTeamAveTime("3729e38b382749ba3a10bae7539e0d9a");
                return AjaxResult.success(info);
            } catch (org.springframework.dao.CannotAcquireLockException e) {
                retryCount++;
                log.warn("班组路径优化数据库锁获取失败，第{}次重试", retryCount);
                if (retryCount < maxRetries) {
                    try {
                        // 等待一段时间后重试
                        Thread.sleep(1000 * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return AjaxResult.error("操作被中断");
                    }
                } else {
                    log.error("班组路径优化重试{}次后仍然失败", maxRetries);
                    return AjaxResult.error("系统繁忙，请稍后重试");
                }
            } catch (Exception e) {
                log.error("班组路径优化发生错误", e);
                return AjaxResult.error("操作失败：" + e.getMessage());
            }
        }

        return AjaxResult.error("系统繁忙，请稍后重试");
    }

    @ApiOperation(value = "班组内路径平衡计算",tags = "a123fae9da370c45984c58720bf3ac7c")
    @GetMapping("/InTeamAveTime")
    public AjaxResult inTeamAveTime( String apikey ) throws ApiKeyException {
        //获取所有已经启用的中转站的id集合
        List<Long> tranIds = routeService.getTranIds();

        // 修复并发事务冲突：改为串行执行，避免数据库锁竞争
        StringBuilder resultBuilder = new StringBuilder();
        for (Long tranId : tranIds) {
            // 添加重试机制，处理数据库锁异常
            int maxRetries = 3;
            int retryCount = 0;
            boolean success = false;

            while (retryCount < maxRetries && !success) {
                try {
                    String result = routeService.asyncAvgTime(tranId, apikey);
                    resultBuilder.append(result);
                    success = true;
                } catch (org.springframework.dao.CannotAcquireLockException e) {
                    retryCount++;
                    log.warn("中转站ID: {} 数据库锁获取失败，第{}次重试", tranId, retryCount);
                    if (retryCount < maxRetries) {
                        try {
                            // 等待一段时间后重试
                            Thread.sleep(1000 * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    } else {
                        log.error("中转站ID: {} 重试{}次后仍然失败", tranId, maxRetries);
                        resultBuilder.append("中转站").append(tranId).append("处理失败(数据库锁);");
                    }
                } catch (Exception e) {
                    log.error("处理中转站ID: {} 时发生错误", tranId, e);
                    // 继续处理其他中转站，不因单个失败而中断整个流程
                    resultBuilder.append("中转站").append(tranId).append("处理失败;");
                    break; // 非锁异常不重试
                }
            }
        }

        return AjaxResult.success(resultBuilder.toString());

        //AjaxResult ajaxResult = optimizingOneGroupRoute(apikey);
        //System.out.println(ajaxResult);
        // return routeService.asyncAvgTime(5L, "3729e38b382749ba3a10bae7539e0d9a");
    }

    @ApiOperation("修改取货时长和聚集区参数后的计算工作时长")
    @GetMapping("/updateWorkTime")
    public AjaxResult updateWorkTime(@RequestParam String apikey) throws ApiKeyException {
        //AjaxResult info = routeService.updateWorkTime(apikey);
        boolean info = routeService.updateWorkTime(apikey);
        if (info) {
            inTeamAveTime(apikey);
        }
        return AjaxResult.success("更新成功");
    }

    @ApiOperation("修改速度后的计算工作时长")
    @GetMapping("/updateWorkTimeOne")
    public AjaxResult updateWorkTimeOne(@RequestBody WorkTimeUpdateRequest request) {
        //AjaxResult info = routeService.updateWorkTime(apikey);
        AjaxResult info = routeService.updateWorkTimeOne(request.getFreeewatDist(), request.getUrabanRoadDist(), request.getTownshipRoadsDist(), request.getLoadingTime());
        System.out.println(info);
        return info;
    }

    @ApiOperation("删除中转站后的工作时长变化")
    @GetMapping("/deleteTranWorkTime")
    public AjaxResult deleteTranWorkTime() {
        AjaxResult info = routeService.deleteTranWorkTime();
        return info;
    }


    @ApiOperation("feign测试")
    @GetMapping("/feign")
    public AjaxResult feign() {
        return AjaxResult.success("feign test");
    }


    @ApiOperation("查询某条路线的所有聚集区耗时")
    @GetMapping("/queryTime")
    public AjaxResult queryTime(Long routeId) {
        double sumTime = routeService.queryTime(routeId);
        return AjaxResult.success(sumTime);
    }

    // ===== 工作参数配置相关接口 =====

    @ApiOperation(value = "获取所有工作参数配置")
    @GetMapping("/workParameter/list")
    public AjaxResult getAllWorkParameters() {
        try {
            log.info("获取所有工作参数配置");
            return AjaxResult.success("获取成功", workParameterService.getAllParameters());
        } catch (Exception e) {
            log.error("获取工作参数配置失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取工作参数配置失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "根据站点名称获取工作参数")
    @GetMapping("/workParameter/getByName")
    public AjaxResult getWorkParameterByName(
            @ApiParam(value = "站点名称", required = true, example = "韶关市")
            @RequestParam("name") String name) {
        try {
            log.info("根据站点名称获取工作参数: {}", name);
            return AjaxResult.success("获取成功", workParameterService.getParameterByName(name));
        } catch (Exception e) {
            log.error("获取工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取工作参数失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "更新工作参数")
    @PostMapping("/workParameter/update")
    public AjaxResult updateWorkParameter(
            @ApiParam(value = "工作参数配置", required = true)
            @Validated @RequestBody com.ict.ycwl.pathcalculate.pojo.WorkParameter workParameter) {
        try {
            log.info("更新工作参数: {}", workParameter.getName());
            boolean success = workParameterService.updateParameter(workParameter);
            if (success) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新工作参数失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "批量更新工作参数")
    @PostMapping("/workParameter/batchUpdate")
    public AjaxResult batchUpdateWorkParameters(
            @ApiParam(value = "工作参数配置列表", required = true)
            @Validated @RequestBody java.util.List<com.ict.ycwl.pathcalculate.pojo.WorkParameter> workParameters) {
        try {
            log.info("批量更新工作参数，数量: {}", workParameters.size());
            boolean success = workParameterService.batchUpdateParameters(workParameters);
            if (success) {
                return AjaxResult.success("批量更新成功");
            } else {
                return AjaxResult.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量更新工作参数失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取全局工作参数")
    @GetMapping("/workParameter/global")
    public AjaxResult getGlobalWorkParameter() {
        try {
            log.info("获取全局工作参数");
            return AjaxResult.success("获取成功", workParameterService.getGlobalParameter());
        } catch (Exception e) {
            log.error("获取全局工作参数失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取全局工作参数失败: " + e.getMessage());
        }
    }
}
